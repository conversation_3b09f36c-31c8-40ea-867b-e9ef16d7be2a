{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover great apps, games, extensions and themes for Chromium.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "/Users/<USER>/.cache/puppeteer/chrome/mac_arm-138.0.7204.94/chrome-mac-arm64/Google Chrome for Testing.app/Contents/Frameworks/Google Chrome for Testing Framework.framework/Versions/138.0.7204.94/Resources/web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chromium PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "/Users/<USER>/.cache/puppeteer/chrome/mac_arm-138.0.7204.94/chrome-mac-arm64/Google Chrome for Testing.app/Contents/Frameworks/Google Chrome for Testing Framework.framework/Versions/138.0.7204.94/Resources/pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "pinned_tabs": [], "protection": {"macs": {"account_values": {"browser": {"show_home_button": "303E0F25DE5B2A481F1B698B27BD3B64A533C3F42B8D7283E0AAB2D7103B8DD8"}, "extensions": {"ui": {"developer_mode": "E692EBBFB77B6AAE377E38D82B62A5021602C60813904F30428B9F66FC4C8A83"}}, "homepage": "889963C0FDD3A69A123E3BDAF9621544C0F702F242BF030A676B09FA3015FE3A", "homepage_is_newtabpage": "69ECEB9CA245168E2BB9E0D74C3FBEB831519296181A1364E0682ECEBA830EA8", "session": {"restore_on_startup": "FE9C92AC007FEB562362813EB01C4F670EFE8102AA2F8382973A7FAF38C462F0", "startup_urls": "8B69B66A514178827AF5A18828EC9BFCFDCC70352ABC919E1D61EE49618AFC36"}}, "browser": {"show_home_button": "9682E35CA8503CC2DA81E9BD5F30FC9E4179CF0CF0C7F189EE5CA0A8A2887E98"}, "default_search_provider_data": {"template_url_data": "1E9B66176229DB75BE831CBDCB328B148BA16B162A976E554EFE2AF8F1AACF18"}, "enterprise_signin": {"policy_recovery_token": "0B7258533516E68ED85F741D2F6320EA4027A7BF800A4F6B28946CE9EE8E6241"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "86801ED32D2CE1D09830723AF7B30D87378BA95F5CC6E46B79FCD32954416FA2", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "3BC4BBE58662FC7BB0A755BF99CA46755C8CC37E91280D72FF56B506B8A89E7C"}, "ui": {"developer_mode": "EEFF39B80C535FEB19D1D236CED573ADDC79035A94A663FCC56FF9B5B1DE8514"}}, "google": {"services": {"account_id": "D8E33C6667EACE50B692BACD93A763D98948F9E907E7610D095BBFFE352E5646", "last_signed_in_username": "503DE069E2D7D570EB26E761AC9DB79683298848D5486201CDCA37F342412FE2", "last_username": "B019306115133104E684DBAF63711A9A0DDD073E76E6854CFCB4171497D80C1B"}}, "homepage": "1C386CB97D3E76EB8C73F8BFB9F91F38FDDA575EF2E26C63AC572F52B7A72689", "homepage_is_newtabpage": "0DCF3FF95FFBE8D50892043F55EA3BB2A129DD32530D0F25A6EC84E259041D4D", "media": {"storage_id_salt": "328985834FD0BCF71EE8F56E317AC43A7264AC518FDB450EA23288F2F251B050"}, "pinned_tabs": "70C177B6ABE13DD67556ADFC8C48549124327F802FA9BAA531C733C5D5C307AD", "prefs": {"preference_reset_time": "78EC8B0D4ED45DAB0BAF3CBB843701486D7949F21A7C6AE25FADA79B30293047"}, "safebrowsing": {"incidents_sent": "B8CDE5B10C7E6B5308CA9BBF849EA5C07CDBD338372B369E70F594B96F512400"}, "search_provider_overrides": "8FE0D008A217A5D81238CC5AAC9CB15499CADBCBA2291C44928E36DD615F5BF2", "session": {"restore_on_startup": "A2F0C8E63230BA802C1883CC1B28F3DF7D8D4493E2A998EE24316BAEAF6B44D9", "startup_urls": "9BFB50012D72514EF73927A6EC051C860C2419D0A1E49EFD607CD151AF70A022"}}, "super_mac": "15E36D66AABA109D82C9F189B5F2E74FD857FCC8653F54516A2B26EAD9A39475"}}