{"disable-features": "AVFoundationCaptureForwardSampleTimestamps<AVFoundationCaptureForwardSampleTimestamps,AcceptCHFrame,AccessibilityPerformanceMeasurementExperiment<AccessibilityPerformanceMeasurementExperiment,AllowSoftwareGLFallbackDueToCrashes<SwiftShaderDeprecation,AllowSwiftShaderFallback<SwiftShaderDeprecation,AutocompleteDictionaryPreload<CompressionDictionaryPreload,AutofillAddressSurvey<AutofillSurveys,AutofillPasswordSurvey<AutofillSurveys,BackForwardCacheNonStickyDoubleFix<BackForwardCacheNonStickyDoubleFix,CompressParkableStrings<DisableCompressParkableStrings,CompressionDictionaryTransportRequireKnownRootCert<CompressionDictionaryTransportRequireKnownRootCert,DefaultAllowPrivacySandboxAttestations<PrivacySandboxAttestationsDefaultDeny,FedCmUpdatedCooldownPeriod<FedCmIntrusionMitigation,FingerprintingProtectionUx<FingerprintingProtectionFilter,IsolateSandboxedIframes,LegacyKeyRepeatSynthesis<LegacyKeyRepeatSynthesis,MediaRouter,MemoryCacheStrongReferenceFilterScripts<MemoryCacheStrongReference,MemoryPurgeInBackground<MemoryPurgeInBackground,MerchantTrustEvaluationControlSurvey<MerchantTrust,MutationEvents<MutationEvents,NonStandardAppearanceValueSliderVertical<NonStandardAppearanceValueSliderVertical,NtpShoppingTasksModule<DesktopNtpModules,NtpWallpaperSearchButtonAnimation<ChromeWallpaperSearchLaunch,OmniboxDocumentProviderEnterpriseEligibilityWhenUnknown<OmniboxDriveEligibility,OptimizationHints,PartitionAllocSortActiveSlotSpans<PartitionAllocMemoryReclaimer,PassageEmbedder<PassageEmbeddingsPerformance,PerformanceControlsBatteryPerformanceSurvey<PerformanceControlsHatsStudy,PerformanceControlsBatterySaverOptOutSurvey<PerformanceControlsHatsStudy,PerformanceControlsPerformanceSurvey<PerformanceControlsHatsStudy,PolicyBlocklistProceedUntilResponse<PolicyBlocklistProceedUntilResponse,PreloadedDictionaryConditionalUse<CompressionDictionaryPreload,ProcessPerSiteUpToMainFrameThreshold,RecordSequenceManagerCrashKeys<CatanCombinedHoldback23H2,ReduceSubresourceResponseStartedIPC<ReduceIPCCombined,RenderDocumentCompositorReuse<RenderDocumentWithNavigationQueueing,SafeBrowsingHashPrefixRealTimeLookupsSamplePing<ExtendedReportingRemovePrefDependency,ScriptStreamingForNonHTTP<WebUIInProcessResourceLoading,SideSearch<SidePanelCompanionDesktopM116Plus,TabGroupsCollapseFreezing<TabGroupsCollapseFreezing,ThreadControllerSetsProfilerMetadata<CatanCombinedHoldback23H2,Translate,UrgentPageDiscarding<DisableUrgentPageDiscarding,UseGles2ForOopR<DisableGles2ForOopR,V8FlushCodeBasedOnTabVisibility<V8CodeFlushing,V8SingleThreadedGCInBackgroundNoIncrementalMarking<V8SingleThreadedGCInBackgroundVariants,V8SingleThreadedGCInBackgroundParallelPause<V8SingleThreadedGCInBackgroundVariants,V8SlowHistograms<V8SlowHistograms,WallpaperSearchSettingsVisibility<ChromeWallpaperSearchGlobal,WebAppsEnableMLModelForPromotion<DesktopPWAInstallPromotionML", "enable-features": "ANGLEPerContextBlobCache<ANGLEPerContextBlobCache,AXRandomizedStressTests<AXRandomizedStressTests,AXTreeFixing<AXTreeFixing,AccessibilityBlockFlowIterator<AXBlockFlowIterator,AccessibilitySerializationSizeMetrics<AccessibilitySerializationSizeMetrics,AdAuctionEventRegistration<ProtectedAudienceClickiness,AggressiveShaderCacheLimits<AggressiveShaderCacheLimits,AiSettingsPageEnterpriseDisabledUi<AiSettingsPageEnterpriseDisabledUi,AllowDatapipeDrainedAsBytesConsumerInBFCache<AllowDatapipeDrainedAsBytesConsumerInBFCache,AllowURNsInIframes<PrivacySandboxAdsAPIs,AlwaysBlock3pcsIncognito<AlwaysBlock3pcsIncognito,AnimationForDesktopCapturePermissionChecker<AnimationForDesktopCapturePermission<PERSON>he<PERSON>,AppleKeychainUseSecItem<MacKeychainApiMigration,AsyncQuicSession<AsyncQuicSession,AsyncSetCookie<FasterSetCookie,AttributionDebugReportingCookieDeprecationTesting<CookieDeprecationFacilitatedTestingCookieDeprecation,AttributionReportingInBrowserMigration<AttributionReportingInBrowserMigration,AudioInputConfirmReadsViaShmem<AudioInputConfirmReadsViaShmem,AutoDisableAccessibility<AutoDisableAccessibility,AutoPictureInPictureForVideoPlayback<AutoPictureInPictureForVideoPlayback,AutoSpeculationRules<AutoSpeculationRules,AutocompleteControllerMetricsOptimization<AutocompleteControllerMetricsOptimization,AutofillAddressSuggestionsOnTyping<AutofillAddressSuggestionsOnTyping,AutofillAddressUserPerceptionSurvey<AutofillAddressUserPerceptionSurveyUS,AutofillAiIgnoreGeoIp<AutofillAiTeamfoodV2,AutofillAiServerModel<AutofillAiTeamfoodInternal,AutofillAiUploadModelRequestAndResponse<AutofillAiTeamfoodInternal,AutofillAiVoteForFormatStringsFromMultipleFields<AutofillAiVoteForFormatStrings,AutofillAiVoteForFormatStringsFromSingleFields<AutofillAiVoteForFormatStrings,AutofillAiWithDataSchema<AutofillAiTeamfoodV2,AutofillBetterLocalHeuristicPlaceholderSupport<AutofillBetterLocalHeuristicPlaceholderSupport,AutofillCardSurvey<AutofillSurveys,AutofillCreditCardUserPerceptionSurvey<AutofillCreditCardUserPerceptionSurvey,AutofillDeduplicateAccountAddresses<AutofillDeduplicateAccountAddresses,AutofillEnableAmountExtractionDesktop<AutofillEnableBuyNowPayLaterDesktop,AutofillEnableBuyNowPayLater<AutofillEnableBuyNowPayLaterDesktop,AutofillEnableCardBenefitsForBmo<AutofillEnableCardBenefitsForBmo,AutofillEnableCardBenefitsIph<AutofillEnableCardBenefitsIph,AutofillEnableExpirationDateImprovements<AutofillEnableExpirationDateImprovements,AutofillEnableFillingPhoneCountryCodesByAddressCountryCodes<AutofillEnableFillingPhoneCountryCodesByAddressCountryCodes,AutofillEnableFpanRiskBasedAuthentication<AutofillEnableFpanRiskBasedAuthentication,AutofillEnableImportWhenMultiplePhoneNumbers<AutofillRelaxAddressImport,AutofillEnableLabelPrecedenceForTurkishAddresses<AutofillEnableLabelPrecedenceForTurkishAddresses,AutofillEnableLogFormEventsToAllParsedFormTypes<AutofillEnableLogFormEventsToAllParsedFormTypes,AutofillEnableRankingFormulaAddressProfiles<AutofillEnableNewCardProfileRankingAlgorithm,AutofillEnableRankingFormulaCreditCards<AutofillEnableNewCardProfileRankingAlgorithm,AutofillEnableSupportForParsingWithSharedLabels<AutofillEnableSupportForParsingWithSharedLabels,AutofillFixSplitCreditCardImport<AutofillFixSplitCreditCardImport,AutofillGreekRegexes<AutofillGreekRegexes,AutofillImproveAddressFieldSwapping<AutofillImproveAddressFieldSwapping,AutofillImproveCityFieldClassification<AutofillImproveCityFieldClassification,AutofillImprovedLabels<AutofillImprovedLabels,AutofillModelPredictions<AutofillModelPredictions,AutofillOptimizeFormExtraction<AutofillOptimizeFormExtraction,AutofillPageLanguageDetection<AutofillPageLanguageDetection,AutofillPasswordUserPerceptionSurvey<AutofillPasswordUserPerceptionSurvey,AutofillPaymentsFieldSwapping<AutofillPaymentsFieldSwapping,AutofillPopupDontAcceptNonVisibleEnoughSuggestion<AutofillPopupDontAcceptNonVisibleEnoughSuggestion,AutofillPopupZOrderSecuritySurface<AutofillPopupZOrderSecuritySurface_V2,AutofillPreferSavedFormAsSubmittedForm<AutofillImproveSubmissionDetectionV2,AutofillRelaxAddressImport<AutofillRelaxAddressImport,AutofillSharedStorageServerCardData<AutofillSharedStorageServerCardData,AutofillStructuredFieldsDisableAddressLines<AutofillStructuredFieldsDisableAddressLines,AutofillSupportLastNamePrefix<AutofillSupportLastNamePrefix,AutofillSupportPhoneticNameForJP<AutofillSupportPhoneticNameForJP,AutofillUKMExperimentalFields<AutofillUKMExperimentalFields,AutofillUnifyRationalizationAndSectioningOrder<AutofillUnifyRationalizationAndSectioningOrder,AutofillUnmaskCardRequestTimeout<AutofillUnmaskCardRequestTimeout,AutofillUpstream<AutofillUpstream,AutofillUseINAddressModel<AutofillI18nINAddressModel,AutofillUseNegativePatternForAllAttributes<AutofillSupportLastNamePrefix,AutofillUseSubmittedFormInHtmlSubmission<AutofillImproveSubmissionDetectionV2,AutofillVcnEnrollStrikeExpiryTime<AutofillVcnEnrollStrikeExpiryTime,AvoidDuplicateDelayBeginFrame<AvoidDuplicateDelayBeginFrame,AvoidEntryCreationForNoStore<AvoidEntryCreationForNoStore,AvoidUnnecessaryBeforeUnloadCheckSync<AvoidUnnecessaryBeforeUnloadCheckSync,AvoidUnnecessaryForcedLayoutMeasurements<AvoidUnnecessaryForcedLayoutMeasurements,BackForwardCachePrioritizedEntry<BackForwardCachePrioritizedEntry,BackForwardCacheSendNotRestoredReasons<BackForwardCacheNotRestoredReasons,BackgroundResourceFetch<BackgroundResourceFetch,BatchNativeEventsInMessagePumpKqueue<BatchNativeEventsInMessagePumpKqueue,BatterySaverModeAlignWakeUps<BatterySaverModeAlignWakeUps,BeaconLeakageLogging<BeaconLeakageLogging,BlinkLifecycleScriptForbidden<BlinkLifecycleScriptForbidden,BlockAcceptClientHints<BlockAcceptClientHints,BookmarkTriggerForPrerender2<Prerender2BookmarkBarTriggerV2,BookmarksUseBinaryTreeInTitledUrlIndex<BookmarksUseBinaryTreeInTitledUrlIndex,BoostRenderProcessForLoading<BoostRenderProcessForLoading,BoundaryEventDispatchTracksNodeRemoval<BoundaryEventDispatchTracksNodeRemoval,BrowserInitiatedAutomaticPictureInPicture<BrowserInitiatedAutomaticPictureInPicture,BrowserSignalsReportingEnabled<BrowserSignalsReportingEnabled,BrowserThreadPoolAdjustment<BrowserThreadPoolAdjustmentForDesktop,BrowsingTopics<PrivacySandboxAdsAPIs,BubbleMetricsApi<BubbleMetricsApi,CSSReadingFlow<CSSReadingFlow,CacheMacSandboxProfiles<CatanCombinedHoldback23H2,CacheSharingForPervasiveScripts<CacheSharingForPervasiveScripts,CacheStorageTaskPriority<CacheStorageTaskPriority,CameraMicPreview<CameraMicPreview,Canvas2DAutoFlushParams<Canvas2DAutoFlushParams,Canvas2DHibernation<CanvasHibernationExperiments,Canvas2DHibernationReleaseTransferMemory<CanvasHibernationExperiments,Canvas2DReclaimUnusedResources<Canvas2DReclaimUnusedResources,CanvasHibernationSnapshotZstd<CanvasHibernationSnapshotZstd,CanvasNoise<CanvasNoise,CanvasTextNg<CanvasTextNg,CastStreamingHardwareHevc<CastStreamingHardwareHevc,CastStreamingMediaVideoEncoder<CastStreamingMediaVideoEncoder,CastStreamingVp9<CastStreamingVp9,CheckHTMLParserBudgetLessOften<CheckHTMLParserBudgetLessOften,ChromeWebStoreNavigationThrottle<ChromeWebStoreNavigationThrottle,ChromeWideEchoCancellation<ChromeWideEchoCancellation,ClearCanvasResourcesInBackground<CanvasHibernationExperiments,ClearCountryPrefForStoredUnknownCountry<SearchEngineChoiceClearInvalidPref,ClearGrShaderDiskCacheOnInvalidPrefix<ClearGrShaderDiskCacheOnInvalidPrefix,ClickToCapturedPointer<ClickToCapturedPointer,ClientSideDetectionAcceptHCAllowlist<ClientSideDetectionAcceptHCAllowlist,ClientSideDetectionLlamaForcedTriggerInfoForScamDetection<ClientSideDetectionLlamaForcedTriggerInfoForScamDetection,ClientSideDetectionRetryLimit<ClientSideDetectionRetryLimit,ClientSideDetectionSamplePing<ClientSideDetectionSamplePing,ClientSideDetectionSendLlamaForcedTriggerInfo<ClientSideDetectionSendLlamaForcedTriggerInfo,CloneDevToolsConnectionOnlyIfRequested<CloneDevToolsConnectionOnlyIfRequested,CoalesceSelectionchangeEvent<CoalesceSelectionchangeEvent,CoalesceStorageAreaCommits<DOMStorageReliabilityEnhancements,CodeBasedRBD<CodeBasedRBD,CommerceLocalPDPDetection<CommerceLocalPDPDetection,CompareConfirmationToast<Compare,Compose<ChromeCompose,ComposeAXSnapshot<ComposeAXSnapshot,ComposeProactiveNudge<ComposeProactiveNudgePosition,ComposeV3Migration<ComposeV3Migration,CompositeBGColorAnimation<CompositeBackgroundColorAnimation,CompositeClipPathAnimation<CompositeClipPathAnimation,CompositorLoadingAnimations<CompositorLoadingAnimations,ConditionalImageResize<ConditionalImageResize,ConfigurableV8CodeCacheHotHours<ConfigurableV8CodeCacheHotHours,ContextualCueing<GlicContextualCueingDogfood,CookieDeprecationFacilitatedTesting<CookieDeprecationFacilitatedTestingCookieDeprecation,CookieSameSiteConsidersRedirectChain<CookieSameSiteConsidersRedirectChainDesktop,CreateURLLoaderPipeAsync<CreateURLLoaderPipeAsync,CustomizableSelect<CustomizableSelect,CustomizeChromeSidePanelExtensionsCard<CustomizeChromeSidePanelExtensionsCard,CustomizeChromeWallpaperSearch<ChromeWallpaperSearchGlobal,CustomizeChromeWallpaperSearchButton<ChromeWallpaperSearchLaunch,CustomizeChromeWallpaperSearchInspirationCard<ChromeWallpaperSearchGlobal,DOMInsertionFaster<RenderingOptimizationsHoldback,DTCKeyRotationUploadedBySharedAPIEnabled<DTCKeyRotationUploadedBySharedAPIEnabled,DataSharing<SharedTabGroups,DataSharingJoinOnly<SharedTabGroups,DecommitPooledPages<DecommitPooledPages,DedicatedWorkerAblationStudyEnabled<PlzDedicatedWorker,DeferImplInvalidation<CompositeBackgroundColorAnimation,DeferSpeculativeRFHCreation<DeferSpeculativeRFHCreation,DelayFirstPeriodicPAPurgeOrReclaim<CatanCombinedHoldback23H2,DelayFirstWorkerWake<CatanCombinedHoldback23H2,DelayLayerTreeViewDeletionOnLocalSwap<RenderDocumentWithNavigationQueueing,DeprecateUnload<DeprecateUnload,DeprecateUnloadByAllowList<DeprecateUnload,DesktopCapturePermissionCheckerPreMacos14_4<DesktopCapturePermissionCheckerPreMacos14_4,DesktopScreenshots<SharingHubDesktopScreenshots,DestroySystemProfiles<DestroySystemProfiles,DetectHiDpiForMsaa<MsaaSettingsMac,DeviceBoundSessionAccessObserverSharedRemote<DeviceBoundSessionAccessObserverSharedRemote,DirectCompositorThreadIpc<DirectCompositorThreadIpcMacLinuxChromeOS,DiscardInputEventsToRecentlyMovedFrames<DiscardInputEventsToRecentlyMovedFrames,DiscountConsentV2<DiscountConsentV2,DiscountDialogAutoPopupBehaviorSetting<DiscountAutoPopup,DlpRegionalizedEndpoints<DlpRegionalizedEndpoints,DoNotEvictOnAXLocationChange<DoNotEvictOnAXLocationChange,DocumentPolicyExpectNoLinkedResources<PreloadInlineDeferredImages,DownloadWarningSurvey<DownloadWarningSurvey,DrawQuadSplitLimit<OcclusionCullingQuadSplitLimit,DwaFeature<DwaFeature,EarlyEstablishGpuChannel<PerfCombined2024,EnableAsyncUploadAfterVerdict<EnableAsyncUploadAfterVerdict,EnableBandAKAnonEnforcement<ProtectedAudiencesEnableBandAKAnonEnforcement,EnableComposeNudgeAtCursor<ComposeProactiveNudgePosition,EnableConfigurableThreadCacheMinCachedMemoryForPurging<ThreadCacheMinCachedMemoryForPurging,EnableConfigurableThreadCacheMultiplier<EnableConfigurableThreadCacheMultiplier,EnableConfigurableThreadCachePurgeInterval<ThreadCachePurgeInterval,EnableDiscountInfoApi<EnableDiscountOnShoppyPagesDesktop,EnableExtensionsExplicitBrowserSignin<EnableExtensionsExplicitBrowserSignin,EnableFingerprintingProtectionFilter<FingerprintingProtectionFilter,EnableFingerprintingProtectionFilterInIncognito<FingerprintingProtectionFilterIncognito,EnableHangWatcher<EnableHangWatcher,EnableHistorySyncOptinExpansionPill<UnoDesktopHistorySyncPillExperiment,EnableICloudKeychainRecoveryFactor<MacICloudKeychainRecoveryFactor,EnableIpPrivacyProxy<IPProtectionPhase0,EnableLazyLoadImageForInvisiblePage<EnableLazyLoadImageForInvisiblePage,EnableOopPrintDrivers<OutOfProcessPrintDriversPrint,EnablePolicyPromotionBanner<EnablePolicyPromotionBanner,EnablePrintWatermark<EnablePrintWatermark,EnableTLS13EarlyData<EnableTLS13EarlyData,EnableUrlRestriction<SavedTabGroupUrlRestriction,EnterpriseFileObfuscation<EnterpriseFileObfuscation,EnterpriseFileSystemAccessDeepScan<EnterpriseFileSystemAccessDeepScan,EnterpriseProfileBadgingForAvatar<DefaultProfileEnterpriseBadging,EnterpriseUpdatedProfileCreationScreen<EnterpriseUpdatedProfileCreationScreen,EsbAsASyncedSetting<EsbAsASyncedSetting,EscapeLtGtInAttributes<EscapeLtGtInAttributes,EstablishGpuChannelAsync<PerfCombined2024,EventTimingIgnorePresentationTimeFromUnexpectedFrameSource<EventTimingIgnorePresentationTimeFromUnexpectedFrameSource,EvictionUnlocksResources<CanvasHibernationExperiments,ExportFrameTimingAfterFrameDone<ExportFrameTimingAfterFrameDoneExperiment,ExtendedReportingRemovePrefDependency<ExtendedReportingRemovePrefDependency,ExtensionManifestV2Unsupported<ExtensionManifestV2Deprecation,ExtensionsCollapseMainMenu<ExtensionsZeroStatePromo,ExtensionsMenuAccessControl<ExtensionsToolbarAndMenuRedesign,ExtremeLightweightUAFDetector<ExtremeLightweightUAFDetector,FailedLoginDetectionBasedOnFormClearEvent<ImprovedFailedLoginDetectionStudy,FailedLoginDetectionBasedOnResourceLoadingErrors<ImprovedFailedLoginDetectionStudy,FastClearNeedsRepaint<RenderingOptimizationsHoldback,FastPathNoRaster<FastPathNoRaster,FedCmCooldownOnIgnore<FedCmIntrusionMitigation,FeedbackIncludeVariations<FeedbackIncludeVariations,FencedFrames<PrivacySandboxAdsAPIs,FencedFramesAPIChanges<PrivacySandboxAdsAPIs,FencedFramesAutomaticBeaconCredentials<FencedFramesEnableCredentialsForAutomaticBeacons,FencedFramesCrossOriginAutomaticBeaconData<FencedFramesEnableCrossOriginAutomaticBeaconData,FencedFramesReportEventHeaderChanges<FencedFramesEnableReportEventHeaderChanges,FencedFramesSrcPermissionsPolicy<FencedFramesEnableSrcPermissionsPolicy,FetchLaterAPI<FetchLaterAPI,FledgeAuctionDealSupport<ProtectedAudienceDealsSupport,FledgeAuctionDownloaderStaleWhileRevalidate<ProtectedAudienceAuctionDownloaderStaleWhileRevalidate,FledgeBidderUseBalancingThreadSelector<ProtectedAudienceBidderUseBalancingThreadSelector,FledgeBiddingAndAuctionNonceSupport<FledgeBiddingAndAuctionNonceSupport,FledgeBiddingAndAuctionServer<FLEDGEBiddingAndAuctionServer,FledgeCacheKAnonHashedKeys<ProtectedAudienceKAnonymityKeyCacheStudy,FledgeClickiness<ProtectedAudienceClickiness,FledgeConsiderKAnonymity<ProtectedAudiencesKAnonymityEnforcementStudy,FledgeDirectFromSellerSignalsHeaderAdSlot<ProtectedAudiencesHeaderDirectFromSellerSignalsStudy,FledgeEagerJSCompilation<ProtectedAudienceEagerJSCompilation,FledgeEnableSampleDebugReportOnCookieSetting<ProtectedAudiencesEnableSampleDebugReportOnCookieSetting,FledgeEnableWALForInterestGroupStorage<PerfCombined2024,FledgeEnforceKAnonymity<ProtectedAudiencesKAnonymityEnforcementStudy,FledgeFacilitatedTestingSignalsHeaders<CookieDeprecationFacilitatedTestingFledgeTrustedSignalsHeaders,FledgeModifyInterestGroupPolicyCheckOnOwner<FledgeModifyInterestGroupPolicyCheckOnOwner,FledgeNoWasmLazyCompilation<ProtectedAudienceNoWasmLazyCompilationStudy,FledgePrepareBidderContextsInAdvance<ProtectedAudiencePrepareBidderContextsStudy,FledgeSellerSignalsRequestsOneAtATime<ProtectedAudienceSellerSignalsRequestsOneAtATime,FledgeSellerWorkletThreadPool<ProtectedAudienceMultiThreadedSellerWorklet,FledgeSendDebugReportCooldownsToBandA<ProtectedAudienceSendDebugReportCooldownsToBandA,FledgeStartAnticipatoryProcesses<ProtectedAudienceEarlyProcessCreationStudy,FledgeTextConversionHelpers<ProtectedAudienceAuctionTextConversionHelpers,FledgeTrustedSignalsKVv1CreativeScanning<ProtectedAudienceTrustedSignalsKVv1CreativeScanning,FledgeUsePreconnectCache<ProtectedAudiencePreconnectCacheStudy,FlushPersistentSystemProfileOnWrite<FlushPersistentSystemProfileOnWrite,FormControlsVerticalWritingModeDirectionSupport<FormControlsVerticalWritingModeDirectionSupport,FormsClassificationsMqlsLogging<AutofillAiTeamfoodV2,ForwardMemoryPressureToBlinkIsolates<MemoryPurgeInBackground,FrameRoutingCache<FrameRoutingCache,FreezingOnBatterySaver<FreezingOnBatterySaver,FurtherOptimizeParsingDataUrls<SimdutfBase64Support,GCMUseDedicatedNetworkThread<GCMUseDedicatedNetworkThread,GCOnArrayBufferAllocationFailure<GCOnArrayBufferAllocationFailure,GetCookiesOnSet<FasterSetCookie,GetUserMediaDeferredDeviceSettingsSelection<CameraMicPreview,Glic<GlicDogfood,GlicAppMenuNewBadge<GlicSettingsDogfood,GlicCSPConfig<GlicSettingsDogfood,GlicClientResponsivenessCheck<GlicSettingsDogfood,GlicFreURLConfig<GlicSettingsDogfood,GlicKeyboardShortcutNewBadge<GlicSettingsDogfood,GlicPageContextEligibility<GlicSettingsDogfood,GlicRollout<GlicRolloutDogfood,GlicTieredRollout<GlicTieredRollout,GlicURLConfig<GlicSettingsDogfood,GlicUserStatusCheck<GlicUserStatusCheckDogfood,GlicZeroStateSuggestions<GlicZeroStateSuggestionsDogfood,GpuYieldRasterization<GpuYieldRasterization,GrCacheLimitsFeature<GrCacheLimits,GwpAsanMalloc<GwpAsan2024WinMac,GwpAsanPartitionAlloc<GwpAsan2024WinMac,HangoutsExtensionV3<HangoutsExtensionV3,HappinessTrackingSurveyForOmniboxOnFocusZps<OmniboxOnFocusZPSV1,HappinessTrackingSurveysForComposeAcceptance<ComposeAcceptanceSurvey,HappinessTrackingSurveysForComposeClose<ComposeCloseSurvey,HappinessTrackingSurveysForDesktopSettings<SettingSearchExplorationHaTS,HappinessTrackingSurveysForDesktopWhatsNew<WhatsNewHats,HappinessTrackingSurveysForHistoryEmbeddings<HappinessTrackingSurveysForHistoryEmbeddings,HappinessTrackingSurveysForSecurityPage<SecurityPageHats,HappinessTrackingSurveysForWallpaperSearch<ChromeWallpaperSearchHaTS,HappyEyeballsV3<HappyEyeballsV3,HarfBuzzBufferPool<RenderingOptimizationsHoldback,HeapProfilerReporting<HeapProfilingLoadFactor,HideDelegatedFrameHostMac<HideDelegatedFrameHostMac,HistoryEmbeddings<HistoryEmbeddingsV2Images,HstsTopLevelNavigationsOnly<HstsTopLevelNavigationsOnly,Http2Grease<HTTP2,HttpCacheNoVarySearch<HttpCacheNoVarySearch,HttpDiskCachePrewarming<HttpDiskCachePrewarming,HttpsFirstBalancedModeAutoEnable<HttpsFirstBalancedModeAutoEnable,HttpsFirstModeV2ForTypicallySecureUsers<HttpsFirstModeV2ForTypicallySecureUsers,IPH_AutofillCreditCardBenefit<AutofillEnableCardBenefitsIph,IPH_BackNavigationMenu<BackNavigationMenuIPH,IPH_DesktopSharedHighlighting<SharedHighlightingIphDesktop,IPH_ExtensionsZeroStatePromo<ExtensionsZeroStatePromo,IPH_GlicPromo<GlicSettingsDogfood,IPH_LensOverlayTranslateButton<ChromnientPostLaunchTranslate,IPH_PerformanceInterventionDialogFeature<PerformanceInterventionAlgorithm,IPH_PriceInsightsPageActionIconLabelFeature<CommercePriceInsights,IPH_PriceTrackingPageActionIconLabelFeature<PriceTrackingPageActionIconLabelFeatureIPH,IPH_ReadingModeSidePanel<ReadAnythingIPHRollout,IPH_SidePanelGenericPinnableFeature<SidePanelPinningWithResponsiveToolbar,IPH_SideSearch<SideSearchInProductHelp,IPH_TabAudioMuting<TabAudioMuting,IPH_TabSearch<TabSearchInProductHelp,IPH_TabSearchToolbarButton<TabstripComboButton,IdbPrioritizeForegroundClients<IdbPrioritizeForegroundClients,IgnoreDiscardAttemptMarker<IgnoreDiscardAttemptMarker,ImageDescriptionsAlternateRouting<ImageDescriptionsAlternateRouting,IncreaseCookieAccessCacheSize<IncreaseCookieAccessCacheSize,IncreasedCmdBufferParseSlice<IncreasedCmdBufferParseSlice,InlineFullscreenPerfExperiment<InlineFullscreenPerfExperiment,InputClosesSelect<CustomizableSelect,InterestGroupStorage<PrivacySandboxAdsAPIs,InterestGroupUpdateIfOlderThan<ProtectedAudiencesUpdateIfOlderThanMs,IsolateFencedFrames<ProcessIsolationForFencedFrames,IsolatedSVGDocumentOptimization<RenderingOptimizationsHoldback,Journeys<GroupedHistoryAllLocales,JourneysOmniboxHistoryClusterProvider<DesktopOmniboxShortcutBoost,KAnonymityService<PrivacySandboxAdsAPIs,KeyboardFocusableScrollers<KeyboardFocusableScrollers,KeyboardLockPrompt<KeyboardLockPrompt,KillSpareRenderOnMemoryPressure<MultipleSpareRPHs,LCPCriticalPathPredictor<LCPPImageLoadingPriority,LCPPAutoPreconnectLcpOrigin<FenderAutoPreconnectLcpOrigins,LCPPDeferUnusedPreload<LCPPDeferUnusedPreload,LCPPFontURLPredictor<LCPPFontURLPredictor,LCPPLazyLoadImagePreload<LCPPLazyLoadImagePreload,LCPPPrefetchSubresource<LCPPPrefetchSubresource,LCPTimingPredictorPrerender2<LCPTimingPredictorPrerender2,LanguageDetectionAPI<LanguageDetectionAPI,LazyBlinkTimezoneInit<LazyBlinkTimezoneInit,LazyUpdateTranslateModel<LazyUpdateTranslateModel,LensImageFormatOptimizations<GoogleLensDesktopImageFormatOptimizations,LensOverlayContextualSearchbox<ContextualSearchBox,LensOverlayLatencyOptimizations<ChromnientLatencyOptimizations,LensOverlaySidePanelOpenInNewTab<ChromnientSidePanelOpenInNewTab,LensOverlaySimplifiedSelection<ChromnientSimplifiedSelection,LensOverlaySurvey<ChromnientSurvey,LensOverlayTranslateButton<ChromnientPostLaunchTranslate,LensOverlayTranslateLanguages<ChromnientMoreTranslateLanguages,LensSearchSidePanelNewFeedback<ChromnientNewFeedback,LensSearchSidePanelScrollToAPI<LensSearchSidePanelScrollToAPI,LensStandalone<GoogleLensDesktopImageFormatOptimizations,LessAggressiveParkableString<ParkableStringsLessAggressiveAndZstd,LineBreakEarlyReturn<RenderingOptimizationsHoldback,LinkPreview<LinkPreview,ListAccountsUsesBinaryFormat<ListAccountsUsesBinaryFormat,LiveCaptionExperimentalLanguages<LiveCaptionExperimentalLanguages,LiveCaptionMultiLanguage<LiveCaptionMultiLanguageRollout,LoadingPhaseBufferTimeAfterFirstMeaningfulPaint<LoadingPhaseBufferTimeAfterFirstMeaningfulPaint,LoadingPredictorLimitPreconnectSocketCount<LoadingPredictorLimitPreconnectSocketCount,LocalIpAddressInEvents<LocalIpAddressInEvents,LocalNetworkAccessChecks<LocalNetworkAccessChecks,LocalWebApprovals<LocalWebApprovalsLinuxMacWindows,LogDuplicateRequests<HttpCacheNoVarySearch,LogOnDeviceMetricsOnStartup<LogOnDeviceMetricsOnStartup,LogUrlScoringSignals<OmniboxLogURLScoringSignals,LongAnimationFrameSourceCharPosition<LongAnimationFrameSourceCharPosition,LowPriorityAsyncScriptExecution<LowPriorityAsyncScriptExecution,LowerHighResolutionTimerThreshold<BatterySaverModeAlignWakeUps,MHTML_Improvements<MHTML_Improvements,MacAccessibilityAPIMigration<MacAccessibilityAPIMigration,MacSetDefaultTaskRole<CatanCombinedHoldback23H2,MachPortRendezvousValidatePeerRequirements<UseAdHocSigningForWebAppShims,MainNodeAnnotations<MainNodeAnnotationsRollout,MainThreadCompositingPriority<CatanCombinedHoldback23H2,ManagedProfileRequiredInterstitial<ManagedProfileRequiredInterstitial,MaskedDomainList<IPProtectionPhase0,MaskedDomainListFlatbufferImpl<IPProtectionMdlImpl,MediaDeviceIdPartitioning<MediaDeviceIdStoragePartitioning,MemoryCacheStrongReference<MemoryCacheStrongReference,MemoryCacheStrongReferenceFilterImages<MemoryCacheStrongReference,MemorySaverModeRenderTuning<MemorySaverModeRenderTuning,MerchantTrust<MerchantTrust,MerchantTrustEvaluationExperimentSurvey<MerchantTrust,MerchantTrustLearnSurvey<MerchantTrust,MetricsLogTrimming<MetricsLogTrimming,MigrateDefaultChromeAppToWebAppsGSuite<MigrateDefaultChromeAppToWebAppsGSuite,MigrateDefaultChromeAppToWebAppsNonGSuite<MigrateDefaultChromeAppToWebAppsNonGSuite,ModelQualityLogging<ComposeModelQualityLogging,MojoBindingsInlineSLS<PerfCombined2024,MojoChannelAssociatedSendUsesRunOrPostTask<MojoChannelAssociatedSendUsesRunOrPostTask,MojoInlineMessagePayloads<MojoInlineMessagePayloads,MojoPredictiveAllocation<MojoPredictiveAllocation,MojoTaskPerMessage<DisableMojoTaskPerMessage,MultiBufferNeverDefer<MultiBufferNeverDefer,MultipleSpareRPHs<MultipleSpareRPHs,NetworkQualityEstimator<NetworkQualityEstimatorParameterTuning,NewContentForCheckerboardedScrolls<NewContentForCheckerboardedScrolls,NewTabPageTriggerForPrerender2<Prerender2NewTabPageTriggerV2,NoPasswordSuggestionFiltering<NoPasswordSuggestionFiltering,NoThrottlingVisibleAgent<NoThrottlingVisibleAgent,NoThrowForCSPBlockedWorker<NoThrowForCSPBlockedWorker,NotificationTelemetry<NotificationTelemetryService,NtpBackgroundImageErrorDetection<DesktopNtpImageErrorDetection,NtpDriveModule<DesktopNtpDriveCache,NtpMiddleSlotPromoDismissal<DesktopNtpMiddleSlotPromoDismissal,NtpMobilePromo<DesktopNtpMobilePromo,NtpModulesLoadTimeoutMilliseconds<DesktopNtpModules,NtpMostRelevantTabResumptionModule<DesktopNtpTabResumption,NtpOneGoogleBarAsyncBarParts<DesktopNtpOneGoogleBarAsyncBarParts,NtpPhotosModule<DesktopNtpModules,NtpSharepointModule<NtpMicrosoftFilesCard,NtpWallpaperSearchButton<ChromeWallpaperSearchLaunch,NtpWallpaperSearchButtonHideCondition<ChromeWallpaperSearchLaunch,NumberOfCoresWithCpuSecurityMitigation<PerfCombined2024,OidcAuthProfileManagement<OidcAuthProfileManagement,OmniboxCalcProvider<DesktopOmniboxCalculatorProvider,OmniboxDocumentProvider<OmniboxDriveEligibility,OmniboxDocumentProviderEnterpriseEligibility<OmniboxDriveEligibility,OmniboxDocumentProviderNoSyncRequirement<OmniboxDriveEligibility,OmniboxDocumentProviderPrimaryAccountRequirement<OmniboxDriveEligibility,OmniboxDomainSuggestions<DesktopOmnibox_HistoryQuickProviderSpecificity,OmniboxFocusTriggersWebAndSRPZeroSuggest<OmniboxOnFocusZPSV1,OmniboxHideSuggestionGroupHeaders<OmniboxOnFocusZPSV1,OmniboxOnDeviceHeadProviderNonIncognito<OmniboxOnDeviceHeadModelSelectionFix,OmniboxOnDeviceTailModel<OmniboxOnDeviceBrainModel,OmniboxRemoveSuggestionsFromClipboard<OmniboxBundledExperimentV1,OmniboxRichAutocompletion<DesktopOmniboxRichAutocompletionMinChar,OmniboxShortcutBoost<DesktopOmniboxShortcutBoost,OmniboxUIExperimentMaxAutocompleteMatches<OmniboxBundledExperimentV1,OmniboxUrlSuggestionsOnFocus<OmniboxOnFocusZPSV1,OomIntervention<MemorySaverModeRenderTuning,OptimizationGuideComposeOnDeviceEval<ComposeOnDeviceModel,OptimizationGuideOnDeviceModel<ComposeOnDeviceModel,OptimizationHintsFetchingSRP<OptGuideBatchSRPTuning,OptimizeHTMLElementUrls<OptimizeHTMLElementUrls,PageActionsMigration<PageActionsMigration,PageInfoAboutThisSiteMoreLangs<PageInfoAboutThisSite40Langs,PaintHoldingForIframes<PaintHoldingOOPIF,PaintLayerUpdateOptimizations<RenderingOptimizationsHoldback,PartitionAllocBackupRefPtr<PartitionAllocBackupRefPtr,PartitionAllocEventuallyZeroFreedMemory<PartialPageZeroing,PartitionAllocFewerMemoryRegions<PartitionAllocFewerMemoryRegions,PartitionAllocLargeThreadCacheSize<PartitionAllocLargeThreadCacheSizeDesktop,PartitionAllocMemoryReclaimer<PartitionAllocMemoryReclaimer,PartitionAllocSchedulerLoopQuarantine<PartitionAllocWithAdvancedChecks,PartitionAllocShortMemoryReclaim<PartitionAllocShortMemoryReclaim,PartitionAllocSortSmallerSlotSpanFreeLists<PartitionAllocMemoryReclaimer,PartitionAllocStraightenLargerSlotSpanFreeLists<PartitionAllocMemoryReclaimer,PartitionAllocUnretainedDanglingPtr<PartitionAllocUnretainedDanglingPtr,PartitionAllocWithAdvancedChecks<PartitionAllocWithAdvancedChecks,PartitionAllocZappingByFreeFlags<PartitionAllocWithAdvancedChecks,PartitionConnectionsByNetworkIsolationKey<PartitionNetworkStateByNetworkAnonymizationKey,PartitionProxyChains<IPProtectionPhase0,PassHistogramSharedMemoryOnLaunch<PassHistogramSharedMemoryOnLaunch,PasswordFormClientsideClassifier<PasswordFormClientsideClassifier,PasswordFormGroupedAffiliations<PasswordFormGroupedAffiliations,Path2DPaintCache<Path2DPaintCache,PdfInfoBar<PdfInfoBar,PdfInk2<PdfInkSignatures,PdfOopif,PdfUseShowSaveFilePicker<PdfUseShowSaveFilePicker,PdfUseSkiaRenderer<PdfUseSkiaRenderer,PerformanceControlsHighEfficiencyOptOutSurvey<PerformanceControlsHatsStudy,PerformanceControlsPPMSurvey<PerformanceControlsPPMSurvey,PerformanceInterventionNotificationImprovements<PerformanceInterventionAlgorithm,PermissionElementPromptPositioning<PermissionElementPromptPositioning,PermissionsAIv1<PermissionsAIv1,PermissionsAIv3<PermissionsAIv3,PermissionsAIv3Geolocation<PermissionsAIv3Geolocation,PermissionsPromptSurvey<CameraMicPreview,PlusAddressAcceptedFirstTimeCreateSurvey<PlusAddressAcceptedFirstTimeCreateSurvey,PlusAddressAndroidOpenGmsCoreManagementPage<PlusAddressesExperiment,PlusAddressDeclinedFirstTimeCreateSurvey<PlusAddressDeclinedFirstTimeCreateSurvey,PlusAddressFallbackFromContextMenu<PlusAddressesExperiment,PlusAddressFilledPlusAddressViaManualFallbackSurvey<PlusAddressFilledPlusAddressViaManualFallbackSurvey,PlusAddressFullFormFill<PlusAddressFullFormFill,PlusAddressPreallocation<PlusAddressesExperiment,PlusAddressSuggestionsOnUsernameFields<PlusAddressSuggestionsOnUsernameFields,PlusAddressUserCreatedMultiplePlusAddressesSurvey<PlusAddressUserCreatedMultiplePlusAddressesSurvey,PlusAddressUserCreatedPlusAddressViaManualFallbackSurvey<PlusAddressUserCreatedPlusAddressViaManualFallbackSurvey,PlusAddressUserDidChooseEmailOverPlusAddressSurvey<PlusAddressUserDidChooseEmailOverPlusAddressSurvey,PlusAddressUserDidChoosePlusAddressOverEmailSurvey<PlusAddressUserDidChoosePlusAddressOverEmailSurvey,PlusAddressesEnabled<PlusAddressesExperiment,PowerBookmarkBackend<PowerBookmarkBackend,PreconnectFromKeyedService<PreconnectFromKeyedService,PreconnectToSearch<PreconnectToSearchDesktop,PrefetchProxy<PrefetchProxyDesktop,PrefetchReusable<PrefetchReusable,PrefetchScheduler<PrefetchScheduler,PrefetchServiceWorker<PrefetchServiceWorker,PrefetchServiceWorkerNoFetchHandlerFix<PrefetchServiceWorkerNoFetchHandlerFix,PrefetchUseContentRefactor<EagerPrefetchBlockUntilHeadDifferentTimeoutsRetrospective,PreloadLinkRelDataUrls<PreloadInlineDeferredImages,PreloadMediaEngagementData<UnifiedAutoplay,PreloadTopChromeWebUILessNavigations<PreloadTopChromeWebUILessNavigations,PreloadingNoSamePageFragmentAnchorTracking<PreloadingNoSamePageFragmentAnchorTracking,Prerender2EarlyDocumentLifecycleUpdate<Prerender2EarlyDocumentLifecycleUpdateV2,Prerender2FallbackPrefetchSpecRules<Prerender2FallbackPrefetchSpecRules,PreserveDiscardableImageMapQuality<PreserveDiscardableImageMapQuality,PreventDuplicateImageDecodes<SpeculativeImageDecodes,PriceInsights<CommercePriceInsights,PriceTrackingSubscriptionServiceLocaleKey<PriceTrackingDesktopExpansionStudy,PrivacyGuideAiSettings<PrivacyGuideAiSettings,PrivacySandboxAdTopicsContentParity<PrivacySandboxPrivacyGuideAdTopics,PrivacySandboxAdsAPIs<PrivacySandboxAdsAPIs,PrivacySandboxAdsAPIsM1Override<PrivacySandboxAdsAPIs,PrivacySandboxAdsApiUxEnhancements<PrivacySandboxAdsApiUxEnhancements,PrivacySandboxAllowPromptForBlocked3PCookies<PrivacySandboxAllowPromptForBlocked3PCookies,PrivacySandboxInternalsDevUI<PrivacySandboxInternalsDevUI,PrivacySandboxMigratePrefsToSchemaV2<PrivacySandboxMigratePrefsToSchemaV2,PrivacySandboxNoticeQueue<PrivacySandboxNoticeQueue,PrivacySandboxRelatedWebsiteSetsUi<PrivacySandboxRelatedWebsiteSetsUi,PrivacySandboxSentimentSurvey<PrivacySandboxSentimentSurvey,PrivateAggregationApi<PrivacySandboxAdsAPIs,PrivateAggregationApiErrorReporting<PrivateAggregationApiErrorReporting,PrivateStateTokens<PrivateStateTokens,ProcessHtmlDataImmediately<ProcessHtmlDataImmediately,ProductSpecifications<Compare,ProductSpecificationsMqlsLogging<Compare,ProfileSignalsReportingEnabled<ProfileSignalsReportingEnabled,ProfilesReordering<ProfilesReordering,ProgrammaticScrollAnimationOverride<ProgrammaticScrollAnimationOverride,ProgressiveAccessibility<ProgressiveAccessibility,PruneOldTransferCacheEntries<PruneOldTransferCacheEntries,PsDualWritePrefsToNoticeStorage<PsDualWritePrefsToNoticeStorage,PushMessagingDisallowSenderIDs<PushMessagingDisallowSenderIDs,PushMessagingGcmEndpointEnvironment<PushMessagingGcmEndpointEnvironment,PwaNavigationCapturing<PWANavigationCapturingV2WindowMacLinux,QueueNavigationsWhileWaitingForCommit<RenderDocumentWithNavigationQueueing,QuicDoesNotUseFeatures<QUIC,QuickIntensiveWakeUpThrottlingAfterLoading<CatanCombinedHoldback23H2,RTCAlignReceivedEncodedVideoTransforms<RTCAlignReceivedEncodedVideoTransforms,ReadAnythingDocsIntegration<ReadAnythingDocsIntegrationRollout,ReadAnythingPermanentAccessibility<ReadAnythingPermanentAccessibility,ReadAnythingReadAloud<ReadAnythingReadAloudDesktop,ReadAnythingReadAloudPhraseHighlighting<ReadAnythingReadAloudPhraseHighlighting,ReadingListEnableSyncTransportModeUponSignIn<UnoDesktopBookmarksAndReadingList,ReclaimOldPrepaintTiles<ReclaimOldPrepaintTiles,ReclaimPrepaintTilesWhenIdle<ReclaimPrepaintTilesWhenIdle,RedWarningSurvey<RedWarningSurvey,ReduceAcceptLanguage<ReduceAcceptLanguage,ReduceAcceptLanguageHTTP<ReduceAcceptLanguageHTTP,ReduceCookieIPCs<CatanCombinedHoldback23H2,ReduceCpuUtilization2<PerfCombined2024,ReduceIPAddressChangeNotification<ReduceIPAddressChangeNotification,ReduceTransferSizeUpdatedIPC<BackgroundResourceFetch,ReleaseResourceDecodedDataOnMemoryPressure<MemoryPurgeInBackground,ReleaseResourceStrongReferencesOnMemoryPressure<MemoryPurgeInBackground,RemotePageMetadata<RemotePageMetadataDesktopExpansion,RemoveCancelledScriptedIdleTasks<RemoveCancelledScriptedIdleTasks,RemoveDataUrlInSvgUse<RemoveDataUrlInSvgUse,RemoveRendererProcessLimit<RemoveRendererProcessLimit,RenderBlockingFullFrameRate<RenderBlockingFullFrameRate,RenderDocument<RenderDocumentWithNavigationQueueing,RendererSideContentDecoding<RendererSideContentDecoding,ReportingServiceAlwaysFlush<ReportingServiceAlwaysFlush,ResolutionBasedDecoderPriority<ResolutionBasedDecoderPriority,ResponsiveToolbar<SidePanelPinningWithResponsiveToolbar,RetryGetVideoCaptureDeviceInfos<RetryGetVideoCaptureDeviceInfos,RunTasksByBatches<CatanCombinedHoldback23H2,RustyPng<RustyPng,SRIMessageSignatureEnforcement<SRIMessageSignatureEnforcement,SafeBrowsingDailyPhishingReportsLimit<SafeBrowsingDailyPhishingReportsLimit,SafeBrowsingExternalAppRedirectTelemetry<SafeBrowsingExternalAppRedirectTelemetry,SafeBrowsingRemoveCookiesInAuthRequests<SafeBrowsingRemoveCookiesInAuthRequests,SafetyCheckUnusedSitePermissions<SafetyCheckUnusedSitePermissions,SafetyHub<SafetyHub,SafetyHubDisruptiveNotificationRevocation<SafetyHubDisruptiveNotificationRevocation,SafetyHubHaTSOneOffSurvey<SafetyHubOneOffHats,ScreenCaptureKitMacScreen<ScreenCaptureKitMacScreen,ScrimForBrowserWindowModal<ScrimForBrowserWindowModal,ScrimForTabModal<ScrimForTabModal,ScrollableAreaOptimization<RenderingOptimizationsHoldback,SeamlessRenderFrameSwap<SeamlessRenderFrameSwap,SearchEnginePreconnect2<SearchEnginePreconnect2,SearchEnginePreconnectInterval<SearchEnginePreconnectInterval,SearchNavigationPrefetch<SearchPrefetchHighPriorityPrefetches,SearchPrefetchWithNoVarySearchDiskCache<HttpCacheNoVarySearch,SegmentationPlatformFedCmUser<FedCmSegmentationPlatform,SegmentationPlatformURLVisitResumptionRanker<DesktopNtpTabResumption,SegmentationPlatformUmaFromSqlDb<SegmentationPlatformUmaFromSqlDb,SelectParserRelaxation<CustomizableSelect,SendExplicitDecodeRequestsImmediately<SpeculativeImageDecodes,SendTabToSelfIOSPushNotifications<SendTabToSelfIOSPushNotifications,ServiceWorkerAutoPreload<ServiceWorkerAutoPreload,ServiceWorkerBackgroundUpdateForRegisteredStorageKeys<ServiceWorkerBackgroundUpdateForRegisteredStorageKeys,ServiceWorkerBypassFetchHandlerHashStrings<ServiceWorkerAutoPreload,ServiceWorkerClientIdAlignedWithSpec<PlzDedicatedWorker,ServiceWorkerStaticRouterRaceNetworkRequestPerformanceImprovement<ServiceWorkerStaticRouterRaceNetworkRequestPerformanceImprovement,SharedDictionaryCache<SharedDictionaryCache,SharedStorageAPI<PrivacySandboxAdsAPIs,SharedStorageAPIEnableWALForDatabase<PerfCombined2024,SharedWorkerBlobURLFix<SharedWorkerBlobURLFix,SharingDisableVapid<SharingDisableVapid,ShoppingList<PriceTrackingDesktopExpansionStudy,ShoppingPDPMetrics<EnablePDPMetricsUSDesktopIOS,ShowSuggestionsOnAutofocus<ShowSuggestionsOnAutofocus,SidePanelCompanion<SidePanelCompanionDesktopM116Plus,SidePanelCompanionChromeOS<SidePanelCompanionDesktopM116Plus,SidePanelPinning<SidePanelPinningWithResponsiveToolbar,SimdutfBase64Support<SimdutfBase64Support,SimpleCachePrioritizedCaching<SimpleCachePrioritizedCaching,SimpleURLLoaderUseReadAndDiscardBodyOption<HttpDiskCachePrewarming,SingleVideoFrameRateThrottling<SingleVideoFrameRateThrottling,SiteInstanceGroupsForDataUrls<SiteInstanceGroupsForDataUrls,SkiaGraphite<SkiaGraphite,SkipPagehideInCommitForDSENavigation<SkipPagehideInCommitForDSENavigation,SkipTpcdMitigationsForAds<CookieDeprecationFacilitatedTestingCookieDeprecation,SonomaAccessibilityActivationRefinements<SonomaAccessibilityActivationRefinements,SpareRPHUseCriticalMemoryPressure<MultipleSpareRPHs,Spark<WhatsNewSparkEdition,SpdyHeadersToHttpResponseUseBuilder<SpdyHeadersToHttpResponseUseBuilder,SpeculativeFixForServiceWorkerDataInDidStartServiceWorkerContext<SpeculativeFixForServiceWorkerDataInDidStartServiceWorkerContext,SpeculativeImageDecodes<SpeculativeImageDecodes,SpeculativeServiceWorkerWarmUp<SpeculativeServiceWorkerWarmUp,SplitCacheByNetworkIsolationKey<SplitCacheByNetworkIsolationKey,SqlFixedMmapSize<SqlFixedMmapSize,SqlScopedTransactionWebDatabase<SqlScopedTransactionWebDatabase,SqlWALModeOnWebDatabase<SqlWalMode,StandardizedBrowserZoom<StandardizedBrowserZoom,StandardizedTimerClamping<StandardizedTimerClamping,StarterPackExpansion<DesktopOmniboxStarterPackExpansion,StarterPackIPH<OmniboxStarterPackIPH,StorageBuckets<StorageBuckets,StreamlineRendererInit<StreamlineRendererInit,SupervisedUserBlockInterstitialV3<LocalWebApprovalsLinuxMacWindows,SuppressesLoadingPredictorOnSlowNetwork<SuppressesNetworkActivitiesOnSlowNetwork,SuppressesPrerenderingOnSlowNetwork<SuppressesNetworkActivitiesOnSlowNetwork,SuppressesSearchPrefetchOnSlowNetwork<SuppressesNetworkActivitiesOnSlowNetwork,SyncEnableBookmarksInTransportMode<UnoDesktopBookmarksAndReadingList,SyncIncreaseNudgeDelayForSingleClient<SyncIncreaseNudgeDelayForSingleClient,SyncPointGraphValidation<SyncPointGraphValidation,TPCDAdHeuristicSubframeRequestTagging<CookieDeprecationFacilitatedTestingCookieDeprecation,TabAudioMuting<TabAudioMuting,TabCaptureInfobarLinks<TabCaptureInfobarLinks,TabGroupShortcuts<TabGroupShortcuts,TabGroupSyncServiceDesktopMigration<TabGroupSyncServiceDesktopMigration,TabHoverCardImages<TabHoverCardImagesMacArm,TabstripComboButton<TabstripComboButton,TabstripDeclutter<TabstripDeclutter,TailoredSecurityIntegration<TailoredSecurityIntegration,TaskManagerDesktopRefresh<TaskManagerDesktopRefresh,TextInputHostMojoCapabilityControlWorkaround<TextInputHostMojoCapabilityControlWorkaround,TextSafetyClassifier<ComposeOnDeviceModel,TextSafetyScanLanguageDetection<TextSafetyScanLanguageDetection,ThrottleUnimportantFrameTimers<ThrottleUnimportantFrameTimers,ToolbarPinning<ToolbarPinning,TraceSiteInstanceGetProcessCreation<TraceSiteInstanceGetProcessCreation,TrackingProtection3pcd<TrackingProtection3pcd,TranslateOpenSettings<TranslateBubbleOpenSettings,TrustSafetySentimentSurvey<TrustSafetySentimentSurvey,TrustSafetySentimentSurveyV2<TrustSafetySentimentSurveyV2,TrustTokens<TrustTokenOriginTrial,UIEnableSharedImageCacheForGpu<UIEnableSharedImageCacheForGpu,UMANonUniformityLogNormal<UMA-NonUniformity-Trial-1-Percent,UMAPseudoMetricsEffect<UMA-Pseudo-Metrics-Effect-Injection-25-Percent,UkmReduceAddEntryIPC<ReduceIPCCombined,UkmSamplingRate<UkmSamplingRate,UnimportantFramesPriority<UnimportantFramePolicy,UnlockDatabaseOnClose<UnlockDatabaseOnClose,UseAdHocSigningForWebAppShims<UseAdHocSigningForWebAppShims,UseBoringSSLForRandBytes<UseBoringSSLForRandBytes,UseCompositorJob<CatanCombinedHoldback23H2,UseDnsHttpsSvcb<DnsHttpsSvcbTimeout,UseSCContentSharingPicker<UseSCContentSharingPicker,UseSmartRefForGPUFenceHandle<UseSmartRefForGPUFenceHandle,UseSnappyForParkableStrings<UseSnappyForParkableStrings,UseZstdForParkableStrings<ParkableStringsLessAggressiveAndZstd,UserBypassUI<UserBypassUI,UserEducationExperienceVersion2Point5<UserEducationExperienceVersion2Point5,UserRemoteCommands<ProfileRemoteCommands,UserRemoteCommandsInvalidationWithDirectMessagesEnabled<ProfileRemoteCommands,UserVisibleProcessPriority<UnimportantFramePolicy,V8EfficiencyModeTiering<V8EfficiencyModeTiering,V8ExternalMemoryAccountedInGlobalLimit<V8ExternalMemoryAccountedInGlobalLimit,V8Flag_discard_memory_pool_before_memory_pressure_gcs<V8DiscardMemoryPoolBeforeMemoryPressureGcs,V8Flag_managed_zone_memory<V8ManagedZoneMemory,V8Flag_zero_unused_memory<PartialPageZeroing,V8FlushBaselineCode<V8CodeFlushing,V8FlushCodeBasedOnTime<V8CodeFlushing,V8GCSpeedUsesCounters<V8GCSpeedUsesCounters,V8IncrementalMarkingStartUserVisible<V8IncrementalMarkingStartUserVisible,V8IntelJCCErratumMitigation<V8IntelJCCErratumMitigation,V8SideStepTransitions<V8SideStepTransitions,V8SingleThreadedGCInBackground<V8SingleThreadedGCInBackgroundVariants,VSyncAlignedPresent<VSyncAlignedPresent,VSyncDecoding<VSyncDecoding,VerifyDidCommitParams<VerifyDidCommitParams,VisibilityAwareResourceScheduler<VisibilityAwareResourceScheduler,VisitedURLRankingService<VisitedURLRankingService,VisualQuerySuggestions<SidePanelCompanionDesktopM116Plus,WaitForLateScrollEvents<WaitForLateScrollEvents,WallpaperSearchGraduated<ChromeWallpaperSearchGlobal,WebAssemblyDeopt<V8WasmDeoptCallIndirectInlining,WebAssemblyInliningCallIndirect<V8WasmDeoptCallIndirectInlining,WebAudioBypassOutputBuffering<WebAudioBypassOutputBuffering,WebContentsDiscard<WebContentsDiscard,WebGPUService<WebGPU,WebRTC-Video-H26xPacketBuffer<WebRTC-Video-H26xPacketBuffer,WebRtcAllowH265Receive<WebRTC-Video-ReceiveAndSendH265,WebRtcAllowH265Send<WebRTC-Video-ReceiveAndSendH265,WebRtcAudioSinkUseTimestampAligner<WebRtcAudioSinkUseTimestampAligner,WebRtcEncodedTransformDirectCallback<WebRtcEncodedTransformDirectCallback,WebUIInProcessResourceLoading<WebUIInProcessResourceLoading,ZeroCopyTabCapture<ZeroCopyTabCaptureStudyMac,ZeroScrollMetricsUpdate<ZeroScrollMetricsUpdate,ZeroSuggestPrefetchDebouncing<ZPSPrefetchDebouncingDesktop,ZeroSuggestPrefetchingOnWeb<OmniboxOnFocusZPSV1,kSpareRPHKeepOneAliveOnMemoryPressure<MultipleSpareRPHs", "force-fieldtrial-params": "AutoSpeculationRules.Enabled_20231201:config/%7B%22framework_to_speculation_rules%22%3A%7B%2212%22%3A%22%7B%5C%22prefetch%5C%22%3A%5B%7B%5C%22source%5C%22%3A%5C%22document%5C%22%2C%5C%22eagerness%5C%22%3A%5C%22conservative%5C%22%2C%5C%22where%5C%22%3A%7B%5C%22href_matches%5C%22%3A%5C%22%2F%2A%5C%22%7D%7D%5D%7D%22%7D%7D/holdback/false,AutofillAddressUserPerceptionSurveyUS.Enabled:en_site_id/Q13fLRHym0ugnJ3q1cK0Tm5d8fMW/probability/1,AutofillCreditCardUserPerceptionSurvey.Enabled:en_site_id/Q13fLRHym0ugnJ3q1cK0Tm5d8fMW/probability/1,AutofillImprovedLabels.Enabled_WithDifferentiatingLabelsInFront:autofill_improved_labels_with_differentiating_labels_in_front/true/autofill_improved_labels_without_main_text_changes/false,AutofillModelPredictions.Enabled:model_active/false,AutofillPasswordUserPerceptionSurvey.Enabled:en_site_id/cLnNGzEX59NNVVEtwumiSF/probability/1,AutofillSurveys.Card_20230606:en_site_id/F2fsskHvB0ugnJ3q1cK0NXLjUaK5/probability/1%2E0,AutofillUKMExperimentalFields.Enabled:autofill_experimental_regex_bucket1/test1,AutofillVcnEnrollStrikeExpiryTime.Enabled:autofill_vcn_strike_expiry_time_days/180,AvoidEntryCreationForNoStore.Enabled:AvoidEntryCreationForNoStoreCacheSize/40000,AvoidUnnecessaryBeforeUnloadCheckSync.WithSendBeforeUnload:AvoidUnnecessaryBeforeUnloadCheckSyncMode/WithSendBeforeUnload,BackNavigationMenuIPH.EnabledIPHWhenUserPerformsChainedBackNavigation_20230510:availability/%3E0/event_trigger/name%3Aback_navigation_menu_iph_is_triggered%3Bcomparator%3A%3C%3D4%3Bwindow%3A365%3Bstorage%3A365/event_used/name%3Aback_navigation_menu_is_opened%3Bcomparator%3A%3D%3D0%3Bwindow%3A7%3Bstorage%3A365/session_rate/%3C1/snooze_params/max_limit%3A4%2Csnooze_interval%3A7/x_experiment/1,BeaconLeakageLogging.Enabled_v1:category_param_name/category/category_prefix/acrcp_v1_,BlockAcceptClientHints.Enabled:BlockedSite/https%3A%2F%2Fwww%2Egoogle%2Ecom,BoostRenderProcessForLoading.Enabled:prioritize_prerendering/false/prioritize_prerendering_only/false/prioritize_renderer_initiated/true/prioritize_restore/false/target_urls/%5B%5D,BrowserThreadPoolAdjustmentForDesktop.thread_pool_default_20230920:BrowserThreadPoolCoresMultiplier/0%2E6/BrowserThreadPoolMax/32/BrowserThreadPoolMin/16/BrowserThreadPoolOffset/0,CacheSharingForPervasiveScripts.Enabled_20250520:url_patterns/https%3A%2F%2Fwww%2Egoogle-analytics%2Ecom%2Fanalytics%2Ejs%0Ahttps%3A%2F%2Fssl%2Egoogle-analytics%2Ecom%2Fga%2Ejs%0Ahttps%3A%2F%2Fwww%2Egoogle-analytics%2Ecom%2Fplugins%2Fua%2Fec%2Ejs%0Ahttps%3A%2F%2Fpagead2%2Egooglesyndication%2Ecom%2Fpagead%2Fmanaged%2Fjs%2Fadsense%2F%2A%2Fshow_ads_impl_fy2021%2Ejs%0Ahttps%3A%2F%2Fpagead2%2Egooglesyndication%2Ecom%2Fpagead%2Fmanaged%2Fjs%2Factiveview%2Fcurrent%2Fufs_web_display%2Ejs%0Ahttps%3A%2F%2Fpagead2%2Egooglesyndication%2Ecom%2Fpagead%2Fmanaged%2Fjs%2Fadsense%2F%2A%2Freactive_library_fy2021%2Ejs%0Ahttps%3A%2F%2Fwww%2Egoogleadservices%2Ecom%2Fpagead%2Fconversion%2Ejs%0Ahttps%3A%2F%2Fwww%2Egoogleadservices%2Ecom%2Fpagead%2Fmanaged%2Fjs%2Factiveview%2Fcurrent%2Freach_worklet%2Ejs%0Ahttps%3A%2F%2Fsecurepubads%2Eg%2Edoubleclick%2Enet%2Fpagead%2Fmanaged%2Fjs%2Fgpt%2F%2A%2Fpubads_impl%2Ejs%0Ahttps%3A%2F%2Fsecurepubads%2Eg%2Edoubleclick%2Enet%2Fpagead%2Fmanaged%2Fjs%2Fgpt%2F%2A%2Fpubads_impl_page_level_ads%2Ejs%0Ahttps%3A%2F%2Ftpc%2Egooglesyndication%2Ecom%2Fpagead%2Fjs%2F%2A%2Fclient%2Fqs_click_protection_fy2021%2Ejs%0Ahttps%3A%2F%2Ftpc%2Egooglesyndication%2Ecom%2Fsafeframe%2F%2A%2Fjs%2Fext%2Ejs%0Ahttps%3A%2F%2Fep2%2Eadtrafficquality%2Egoogle%2Fsodar%2Fsodar2%2Ejs,CameraMicPreview.CameraOther_20250408:action_filter/Accepted%2CAcceptedOnce%2CDismissed/probability/0%2E6/prompt_disposition_reason_filter/DefaultFallback/request_type_filter/VideoCapture/survey_display_time/OnPromptResolved/trigger_id/jGykX8MLD0ugnJ3q1cK0NzqCtGvZ,Canvas2DAutoFlushParams.Candidate:max_pinned_image_kb/32768/max_recorded_op_kb/2048,CanvasNoise.Enabled:enable_in_regular_mode/true,ChromeWallpaperSearchHaTS.Enabled:WallpaperSearchHatsDelayParam/18s/en_site_id/foo/probability/1%2E0,ChromeWallpaperSearchLaunch.DefaultOnLaunched:NtpWallpaperSearchButtonHideConditionParam/1,ChromeWideEchoCancellation.Enabled_20220412:processing_fifo_size/110,ChromnientLatencyOptimizations.AllOptimizationsEnabled:enable-cluster-info-optimization/true/enable-early-interaction-optimization/true/enable-early-start-query-flow-optimization/true,ChromnientSurvey.Enabled:en_site_id/LvUA3D5Ts0ugnJ3q1cK0SHbD7uPa/probability/0%2E5/results-time/6s,ClientSideDetectionRetryLimit.Enabled:RetryTimeMax/15,CodeBasedRBD.Enabled_20230420:code-based-rbd/true,CommercePriceInsights.Enabled:availability/any/event_1/name%3Aprice_insights_page_action_icon_label_in_trigger%3Bcomparator%3Aany%3Bwindow%3A0%3Bstorage%3A360/event_trigger/name%3Aprice_insights_page_action_icon_label_in_trigger%3Bcomparator%3Aany%3Bwindow%3A0%3Bstorage%3A360/event_used/name%3Aprice_insights_page_action_icon_label_used%3Bcomparator%3Aany%3Bwindow%3A0%3Bstorage%3A360/price-insights-show-feedback/true/session_rate/any,ComposeAcceptanceSurvey.Enabled:en_site_id/44m1DgehL0ugnJ3q1cK0Qih71MRQ/probability/0%2E1,ComposeCloseSurvey.Enabled:en_site_id/mT2d9fiNR0ugnJ3q1cK0SdAewrT2/probability/0%2E1,ComposeModelQualityLogging.ComposeLoggingEnabled_Dogfood:model_execution_feature_compose/true,ComposeOnDeviceModel.Enabled:on_device_retract_unsafe_content/false/on_device_text_safety_token_interval/10,ComposeProactiveNudgePosition.Enabled_CursorNudgeModel:proactive_nudge_compact_ui/true/proactive_nudge_delay_milliseconds/1000/proactive_nudge_force_show_probability/0%2E04/proactive_nudge_show_probability/0%2E02,ConfigurableV8CodeCacheHotHours.cache_72h_20230904:V8CodeCacheHotHours/72,ContextualSearchBox.Enabled_NoAutoFocus_20250421:auto-focus-searchbox/false/page-content-request-id-fix/true/pdf-text-character-limit/5000/send-page-url-for-contextualization/true/show-contextual-searchbox-ghost-loader-loading-state/true/update-viewport-each-query/true/use-apc-as-context/true/use-inner-html-as-context/false/use-inner-text-as-context/true/use-pdf-interaction-type/true/use-pdf-vit-param/true/use-pdfs-as-context/true/use-updated-content-fields/true/use-webpage-interaction-type/true/use-webpage-vit-param/true,CookieDeprecationFacilitatedTestingCookieDeprecation.Treatment_PreStable_20231002:SkipTpcdMitigationsForAdsHeuristics/true/SkipTpcdMitigationsForAdsMetadata/true/SkipTpcdMitigationsForAdsSupport/true/decision_delay_time/1s/disable_3p_cookies/true/disable_ads_apis/false/enable_otr_profiles/false/enable_silent_onboarding/false/label/prestable_treatment_1/need_onboarding_for_label/true/need_onboarding_for_synthetic_trial/true/use_profile_filtering/false/version/2,DeferSpeculativeRFHCreation.EnabledWithPrewarmAndDelay:create_speculative_rfh_delay_ms/1/create_speculative_rfh_filter_restore/true/wait_until_final_response/true/warmup_spare_process/true,DeprecateUnload.Enabled_135:allowlist/a%2Ecom%2Cb%2Ecom%2Cc%2Ecom%2Cd%2Ecom%2Ce%2Ecom%2Cf%2Ecom%2Cweb-platform%2Etest%2Cwww1%2Eweb-platform%2Etest%2C127%2E0%2E0%2E1%2Cexample%2Etest%2Cwww%2Egoogle%2Ecom/rollout_percent/0,DesktopNtpDriveCache.Cache_1m:NtpDriveModuleCacheMaxAgeSParam/60/NtpDriveModuleExperimentGroupParam/experiment_1234,DesktopNtpModules.RecipeTasksRuleBasedDiscountDriveManagedUsersCartOptimizeRecipeTasksSAPIV2Fre_Enabled:NtpModulesLoadTimeoutMillisecondsParam/3000/use_sapi_v2/true,DesktopNtpTabResumption.TabResumption_Random:use_random_score/true,DesktopOmniboxRichAutocompletionMinChar.Enabled_1_v1:RichAutocompletionAutocompleteShortcutTextMinChar/1/RichAutocompletionAutocompleteTitlesMinChar/1,DesktopOmniboxShortcutBoost.Enabled:ShortcutBoostGroupWithSearches/true/ShortcutBoostNonTopHitSearchThreshold/2/ShortcutBoostNonTopHitThreshold/2/ShortcutBoostSearchScore/1414/ShortcutBoostUrlScore/1414/omnibox_history_cluster_provider_inherit_search_match_score/true/omnibox_history_cluster_provider_score/1414,DesktopOmnibox_HistoryQuickProviderSpecificity.Enabled:DomainSuggestionsAlternativeScoring/true/DomainSuggestionsMaxMatchesPerDomain/2/DomainSuggestionsMinInputLength/4/DomainSuggestionsScoreFactor/1/DomainSuggestionsTypedUrlsOffset/1/DomainSuggestionsTypedUrlsThreshold/7/DomainSuggestionsTypedVisitCapPerVisit/2/DomainSuggestionsTypedVisitOffset/1/DomainSuggestionsTypedVisitThreshold/4,DesktopPWAInstallPromotionML.Disabled:guardrail_report_prob/0%2E5/max_days_to_store_guardrails/180/model_and_user_decline_report_prob/0%2E5,DiscardInputEventsToRecentlyMovedFrames.DoNotDiscard:distance_factor/100000%2E/time_ms/0,DiscountAutoPopup.Enabled:history-cluster-behavior/1/merchant-wide-behavior/2/non-merchant-wide-behavior/0,DiscountConsentV2.enabled_ntp_native_dialog_with_approved_strings_M104_20220809:discount-consent-ntp-variation/4/step-one-static-content/Let+Google+help+you+find+discounts+for+your+carts/step-one-use-static-content/true,DnsHttpsSvcbTimeout.Enabled:UseDnsHttpsSvcbInsecureExtraTimeMax/500ms/UseDnsHttpsSvcbInsecureExtraTimeMin/300ms/UseDnsHttpsSvcbInsecureExtraTimePercent/50/UseDnsHttpsSvcbSecureExtraTimeMax/500ms/UseDnsHttpsSvcbSecureExtraTimeMin/300ms/UseDnsHttpsSvcbSecureExtraTimePercent/50,DownloadWarningSurvey.DownloadBubbleBypass_20240513:en_site_id/ikozJtokP0ugnJ3q1cK0SbwHjMKE/probability/1%2E0/survey_type/0,EagerPrefetchBlockUntilHeadDifferentTimeoutsRetrospective.Enabled:block_until_head_timeout_eager_prefetch/500,EnableAsyncUploadAfterVerdict.Enabled:max_parallel_requests/15,EnableConfigurableThreadCacheMultiplier.multiplier_2%2E0_20230904:ThreadCacheMultiplier/2/ThreadCacheMultiplierForAndroid/1,EnableDiscountOnShoppyPagesDesktop.Enabled:discount-on-shoppy-page/true,EnableHangWatcher.Enabled:io_thread_log_level/2/ui_thread_log_level/2/utility_process_main_thread_log_level/2,EnableLazyLoadImageForInvisiblePage.Enabled_AllInvisiblePage:enabled_page_type/all_invisible_page,ExtensionsZeroStatePromo.Variant1:x_iph-variant/custom-action-iph,ExtremeLightweightUAFDetector.Quarantine_900_100_512_v6:object_size_threshold_in_bytes/512/quarantine_capacity_for_large_objects_in_bytes/0/quarantine_capacity_for_small_objects_in_bytes/1048576/sampling_frequency/100/target_processes/browser_only,FLEDGEBiddingAndAuctionServer.Enabled:FledgeBiddingAndAuctionKeyConfig/%7B%22https%3A%2F%2Fpublickeyservice%2Egcp%2Eprivacysandboxservices%2Ecom%22%3A+%22https%3A%2F%2Fpublickeyservice%2Epa%2Egcp%2Eprivacysandboxservices%2Ecom%2F%2Ewell-known%2Fprotected-auction%2Fv1%2Fpublic-keys%22%2C+%22https%3A%2F%2Fpublickeyservice%2Epa%2Egcp%2Eprivacysandboxservices%2Ecom%22%3A+%22https%3A%2F%2Fpublickeyservice%2Epa%2Egcp%2Eprivacysandboxservices%2Ecom%2F%2Ewell-known%2Fprotected-auction%2Fv1%2Fpublic-keys%22%2C+%22https%3A%2F%2Fpublickeyservice%2Epa%2Eaws%2Eprivacysandboxservices%2Ecom%22%3A+%22https%3A%2F%2Fpublickeyservice%2Epa%2Eaws%2Eprivacysandboxservices%2Ecom%2F%2Ewell-known%2Fprotected-auction%2Fv1%2Fpublic-keys%22%7D/FledgeBiddingAndAuctionKeyURL/https%3A%2F%2Fpublickeyservice%2Epa%2Egcp%2Eprivacysandboxservices%2Ecom%2F%2Ewell-known%2Fprotected-auction%2Fv1%2Fpublic-keys,FedCmIntrusionMitigation.CooldownOnIgnore:FirstDismissal/24/FourthDismissal/28/SecondDismissal/5/ThirdDismissal/14,FenderAutoPreconnectLcpOrigins.EnabledWithOne_20240214:lcpp_preconnect_frequency_threshold/0%2E5/lcpp_preconnect_max_origins/1,FingerprintingProtectionFilter.Enabled_20250331:activation_level/dry_run/enable_only_if_3pc_blocked/false/performance_measurement_rate/1%2E0,FingerprintingProtectionFilterIncognito.Enabled_20250416:enable_console_logging/true/performance_measurement_rate/1%2E0,GlicSettingsDogfood.Enabled:glic-allowed-origins-override/https%3A%2F%2Fwww%2Egoogle%2Ecom/glic-client-responsiveness-check-interval-ms/5000/glic-fre-url/https%3A%2F%2Fwww%2Egoogle%2Ecom%2F%3F/glic-guest-url/https%3A%2F%2Fwww%2Egoogle%2Ecom%2F/glic-shortcuts-launcher-toggle-learn-more-url/https%3A%2F%2Fsupport%2Egoogle%2Ecom%2F/glic-shortcuts-learn-more-url/https%3A%2F%2Fsupport%2Egoogle%2Ecom%2F/glic-shortcuts-location-toggle-learn-more-url/https%3A%2F%2Fsupport%2Egoogle%2Ecom%2F/glic-shortcuts-tab-access-toggle-learn-more-url/https%3A%2F%2Fsupport%2Egoogle%2Ecom%2F,GlicUserStatusCheckDogfood.Enabled:glic-user-status-oauth2-scope/https%3A%2F%2Fwww%2Egoogleapis%2Ecom%2Fauth%2Fgemini/glic-user-status-request-delay/23h/glic-user-status-url/https%3A%2F%2Fwww%2Eexample%2Ecom%2F,GoogleLensDesktopImageFormatOptimizations.WebpQualityBackendV6:dismiss-loading-state-on-document-on-load-completed-in-primary-main-frame/false/dismiss-loading-state-on-navigation-entry-committed/true/encoding-quality-image-search/45/encoding-quality-region-search/45/lens-homepage-url/https%3A%2F%2Flens%2Egoogle%2Ecom%2Fv3%2F/lens-html-redirect-fix/false/use-jpeg-for-image-search/false/use-webp-for-image-search/true,GrCacheLimits.cache_96_256_8_default_20230911:MaxDefaultGlyphCacheTextureBytes/8388608/MaxGaneshResourceCacheBytes/100663296/MaxHighEndGaneshResourceCacheBytes/268435456,GroupedHistoryAllLocales.Enabled:JourneysLocaleOrLanguageAllowlist/%2A,GwpAsan2024WinMac.CombinedGreedyArmForVariationsPresubmit:BrowserAllocationSamplingMultiplier/800/BrowserAllocationSamplingRange/10/BrowserMaxAllocations/210/BrowserMaxMetadata/765/BrowserTotalPages/6144/GpuAllocationSamplingMultiplier/800/GpuAllocationSamplingRange/10/GpuMaxAllocations/140/GpuMaxMetadata/510/GpuTotalPages/4096/RendererAllocationSamplingMultiplier/600/RendererAllocationSamplingRange/10,HTTP2.Enabled6:http2_grease_settings/true,HappinessTrackingSurveysForHistoryEmbeddings.Enabled:en_site_id/TKWU6DjUi0ugnJ3q1cK0Z69WFqDB/probability/0%2E5,HeapProfilingLoadFactor.Enabled:browser-hash-set-load-factor/0%2E7/gpu-hash-set-load-factor/0%2E7/network-hash-set-load-factor/0%2E7/renderer-hash-set-load-factor/0%2E7/utility-hash-set-load-factor/0%2E7,HistoryEmbeddingsV2Images.Enabled:EnableImagesForResults/true,HttpDiskCachePrewarming.WithReadAndDiscardBody_20240328:http_disk_cache_prewarming_main_resource/false/http_disk_cache_prewarming_use_read_and_discard_body_option/true,IPProtectionPhase0.enabled_baseline_2025-03-19_Stable_Perf_External:IpPrivacyAuthTokenCacheBatchSize/64/IpPrivacyAuthTokenCacheLowWaterMark/16/IpPrivacyBsaEnablePrivacyPass/true/IpPrivacyCacheTokensByGeo/true/IpPrivacyDebugExperimentArm/6/IpPrivacyDirectOnly/false/IpPrivacyUseProxyChains/true/IpPrivacyUseQuicProxies/false/MaskedDomainListExperimentalVersion/,KeyboardLockPrompt.Enabled_PEPC:use_pepc_ui/true,LCPPDeferUnusedPreload.EnableWithPostTask_20240426:load_timing/post_task,LCPPFontURLPredictor.Enabled:lcpp_font_prefetch_threshold/1%2E1/lcpp_font_url_frequency_threshold/0%2E1/lcpp_max_font_url_count_per_origin/5/lcpp_max_font_url_length/1024/lcpp_max_font_url_to_preload/1,LCPPImageLoadingPriority.MediumPriority_20240418:lcpp_adjust_image_load_priority/true/lcpp_adjust_image_load_priority_confidence_threshold/0%2E0/lcpp_adjust_image_load_priority_override_first_n_boost/true/lcpp_enable_image_load_priority_for_htmlimageelement/false/lcpp_enable_perf_improvements/true/lcpp_image_load_priority/medium/lcpp_max_element_locator_length/1024/lcpp_max_histogram_buckets/10/lcpp_max_hosts_to_track/100/lcpp_recorded_lcp_element_types/image_only/lcpp_sliding_window_size/1000,LCPPLazyLoadImagePreload.EnableWithNativeLazyLoading_20231113:lcpp_preload_lazy_load_image_type/native_lazy_loading,LinkPreview.EnabledAltClick:trigger_type/alt_click,LiveCaptionExperimentalLanguages.Enabled:available_languages/en-US%2Cfr-FR%2Cit-IT%2Cde-DE%2Ces-ES%2Cja-JP%2Chi-IN%2Cpt-BR%2Cko-KR%2Cpl-PL%2Cth-TH%2Ctr-TR%2Cid-ID%2Ccmn-Hans-CN%2Ccmn-Hant-TW%2Cvi-VN%2Cru-RU,LiveCaptionMultiLanguageRollout.Enabled:available_languages/en-US%2Cfr-FR%2Cit-IT%2Cde-DE,LoadingPhaseBufferTimeAfterFirstMeaningfulPaint.buffer_500ms_20230907:LoadingPhaseBufferTimeAfterFirstMeaningfulPaintMillis/500,LocalNetworkAccessChecks.EnabledWarning:LocalNetworkAccessChecksWarn/true,LogOnDeviceMetricsOnStartup.Enabled:on_device_startup_metric_delay/3m,LowPriorityAsyncScriptExecution.Enabled:delay_async_exec_opt_out_auto_fetch_priority_hint/false/delay_async_exec_opt_out_high_fetch_priority_hint/true/delay_async_exec_opt_out_low_fetch_priority_hint/false/low_pri_async_exec_cross_site_only/true/low_pri_async_exec_disable_when_lcp_not_in_html/false/low_pri_async_exec_exclude_document_write/true/low_pri_async_exec_exclude_non_parser_inserted/false/low_pri_async_exec_feature_limit/3s/low_pri_async_exec_lower_task_priority/low/low_pri_async_exec_main_frame_only/true/low_pri_async_exec_target/non_ads/low_pri_async_exec_timeout/1s,MemorySaverModeRenderTuning.Enabled:available_memory_threshold_mb/740,MerchantTrust.Enabled_20250130:en_site_id/pnM2xW83W0ugnJ3q1cK0WyyikxbT/probability/1%2E0,MultipleSpareRPHs.Enabled2:count/2,NetworkQualityEstimatorParameterTuning.Experiment:HalfLifeSeconds/15/RecentEndToEndThresholdInSeconds/60/RecentHTTPThresholdInSeconds/60/RecentTransportThresholdInSeconds/60,NtpMicrosoftFilesCard.Enabled_NonInsights:NtpSharepointModuleDataParam/non-insights,OcclusionCullingQuadSplitLimit.quad_split_limit_8:num_of_splits/8,OmniboxLogURLScoringSignals.Enabled:enable_scoring_signals_annotators/true,OmniboxOnDeviceHeadModelSelectionFix.Fix:SelectionFix+/true,OmniboxOnFocusZPSV1.Enabled_6_Suggestions:OnFocusMaxSearchSuggestions/3/OnFocusMaxUrlSuggestions/3/OnFocusMostVisitedMaxSuggestions/6,OptGuideBatchSRPTuning.Enabled_20240624:max_urls_for_srp_fetch/10,OptimizeHTMLElementUrls.Enabled:cache_size/100,OutOfProcessPrintDriversPrint.Enabled_20230912:EarlyStart/false/JobPrint/true/Sandbox/false,PWANavigationCapturingV2WindowMacLinux.EnabledSettingOnByDefault20241105:link_capturing_state/reimpl_default_on,PageActionsMigration.Enabled:lens_overlay/true/memory_saver/true/offer_notification/true/translate/true,PartitionAllocBackupRefPtr.Enabled:enabled-processes/all-processes,PartitionAllocLargeThreadCacheSizeDesktop.size_32768_20230925:PartitionAllocLargeThreadCacheSizeValue/32768,PartitionAllocMemoryReclaimer.Interval_8sec:interval/8s/mode/only-when-unprovisioning,PartitionAllocUnretainedDanglingPtr.Enabled:mode/dump_without_crashing,PartitionAllocWithAdvancedChecks.Enabled:PartitionAllocSchedulerLoopQuarantineBrowserUICapacity/1048576/enabled-processes/browser-only,PdfInfoBar.EnabledPdfLoad:trigger/pdf-load,PerformanceControlsHatsStudy.EnabledHighEfficiencyOptOut_20230223:en_site_id/hEedoxCS30ugnJ3q1cK0YKKzXjSm/probability/1%2E0,PerformanceControlsPPMSurvey.Enabled_Uniform_20250526:en_site_id/N5wFxEDQr0ugnJ3q1cK0SNopqcEc/hats_histogram_name/Feedback%2EHappinessTrackingSurvey%2EPerformanceControlsPPMSurvey/hats_survey_ukm_id/1027171324/ppm_survey_segment_max_memory_gb1/8/ppm_survey_segment_name1/Mac%2C+up+to+8+GB/ppm_survey_segment_name2/Mac%2C+over+8+GB/ppm_survey_uniform_sample/true/probability/0%2E252/survey/performance-ppm,PerformanceInterventionAlgorithm.EnabledLessAggressive_20250428:availability/any/event_trigger/name%3Aperformance_intervention_dialog_trigger%3Bcomparator%3A%3C3%3Bwindow%3A1%3Bstorage%3A360/event_used/name%3Aperformance_intervention_dialog_used%3Bcomparator%3Aany%3Bwindow%3A0%3Bstorage%3A360/event_weekly_trigger/name%3Aperformance_intervention_dialog_trigger%3Bcomparator%3A%3C21%3Bwindow%3A7%3Bstorage%3A360/minimum_time_reshow/1h/no_acceptance_back_off/7d/scale_max_times_per_day/3/scale_max_times_per_week/21/session_rate/any/window_size/10,PermissionElementPromptPositioning.NearElement:PermissionElementPromptPositioningParam/near_element,PreconnectFromKeyedService.EnabledWithOTRtrue_20250319:run_on_otr/true,PreconnectToSearchDesktop.EnabledWithStartupDelayForegroundOnly:skip_in_background/true/startup_delay_ms/5000,PrefetchReusable.Enabled_20250129:prefetch_reusable_body_size_limit/4194304,PrefetchScheduler.Enabled:kPrefetchSchedulerProgressSyncBestEffort/true,Prerender2BookmarkBarTriggerV2.MouseDownAndMouseHover300ms_20241008:prerender_bookmarkbar_on_mouse_hover_trigger/true/prerender_bookmarkbar_on_mouse_pressed_trigger/true/prerender_start_delay_on_mouse_hover_ms/300,Prerender2FallbackPrefetchSpecRules.Enabled_NoTimeout_Burst:kPrerender2FallbackBodySizeLimit/4194304/kPrerender2FallbackPrefetchReusablePolicy/UseIfIsLikelyAheadOfPrerender/kPrerender2FallbackPrefetchSchedulerPolicy/Burst/kPrerender2FallbackPrefetchUseBlockUntilHeadTimetout/false,Prerender2NewTabPageTriggerV2.BothMouseDownAndMouseHover_20241010:prerender_new_tab_page_on_mouse_hover_trigger/true/prerender_new_tab_page_on_mouse_pressed_trigger/true/prerender_start_delay_on_mouse_hover_ms/300,PriceTrackingPageActionIconLabelFeatureIPH.Enabled:availability/any/event_trigger/name%3Aprice_tracking_page_action_icon_label_in_trigger%3Bcomparator%3A%3D%3D0%3Bwindow%3A1%3Bstorage%3A365/event_used/name%3Aused%3Bcomparator%3Aany%3Bwindow%3A365%3Bstorage%3A365/session_rate/any,PrivacySandboxAdsAPIs.Enabled_Notice_M1_AllAPIs_Expanded_NoOT_Stable:implementation_type/mparch,PrivacySandboxSentimentSurvey.Enabled:probability/1%2E0/sentiment-survey-trigger-id/EHJUDsZQd0ugnJ3q1cK0Ru5GreU3,ProcessHtmlDataImmediately.AllChunks:first/true/main/true/rest/true,ProgrammaticScrollAnimationOverride.Enabled:cubic_bezier_x1/0%2E4/cubic_bezier_x2/0%2E0/cubic_bezier_y1/0%2E0/cubic_bezier_y2/1%2E0/max_animation_duration/1%2E5s,ProtectedAudienceMultiThreadedSellerWorklet.TwoThreads:seller_worklet_thread_pool_size/2,PushMessagingGcmEndpointEnvironment.Enabled_Dogfood:PushMessagingGcmEndpointUrl/https%3A%2F%2Fjmt17%2Egoogle%2Ecom%2Ffcm%2Fsend%2F,QUIC.Enabled:channel/F/epoch/30000000/retransmittable_on_wire_timeout_milliseconds/200,ReadAnythingIPHRollout.Enabled:availability/any/distillable_urls/support%2Egoogle%2Ecom%2Cdocs%2Egoogle%2Ecom/event_trigger/name%3Aiph_reading_mode_side_panel_trigger%3Bcomparator%3A%3C%3D3%3Bwindow%3A360%3Bstorage%3A360/event_used/name%3Areading_mode_side_panel_shown%3Bcomparator%3A%3D%3D0%3Bwindow%3A360%3Bstorage%3A360/session_rate/%3D%3D0,RedWarningSurvey.RedInterstitial_20241212:RedWarningSurveyDidProceedFilter/TRUE%2CFALSE/RedWarningSurveyTriggerId/ZdMdCoDc50ugnJ3q1cK0VwqVnKb6/probability/1%2E0,RemotePageMetadataDesktopExpansion.Enabled_20240514:supported_countries/%2A/supported_locales/%2A,RenderBlockingFullFrameRate.Enabled:throttle-frame-rate-on-initialization/true,RenderDocumentWithNavigationQueueing.EnabledSubframeWithQueueing:level/subframe/queueing_level/full,SafeBrowsingDailyPhishingReportsLimit.Enabled:kMaxReportsPerIntervalESB/10,SafetyHub.Enabled:background-password-check-interval/30d,SafetyHubDisruptiveNotificationRevocation.ShadowRun_20250227:max_engagement_score/0/min_notification_count/3/shadow_run/true,SafetyHubOneOffHats.SafetyHub_NoNotification:probability/0%2E0155/safety-hub-ab-control-trigger-id/XxxasK3Zu0ugnJ3q1cK0Yv2LaPos/survey/safety-hub-control,SearchEnginePreconnect2.EnabledWithbase_60_30_30_30__20250507:IdleTimeoutInSeconds/60/MaxPreconnectRetryInterval/30/MaxShortSessionThreshold/30/PingIntervalInSeconds/30,SearchEnginePreconnectInterval.EnabledWith50_20250114:preconnect_interval/50,SearchPrefetchHighPriorityPrefetches.EnabledHighPriorityBothTriggers_20230721:mouse_down/true/navigation_prefetch_param/op/up_or_down/true,SecurityPageHats.Enabled:probability/1/security-page-time/15s/security-page-trigger-id/c4dvJ3Sz70ugnJ3q1cK0SkwJZodD,SendTabToSelfIOSPushNotifications.Enabled:variant_with_magic_stack_card/true,ServiceWorkerAutoPreload.Enabled:script_checksum_to_bypass/144B3D5485B621F5896FD51D6A6FE66A07B6DBC3ACF6404748A833C2D592F900/use_allowlist/true,SettingSearchExplorationHaTS.Enabled:en_site_id/JcjxgSDnh0ugnJ3q1cK0UVkwDj1o/probability/1%2E0/settings-time/10s/survey/settings,SharedDictionaryCache.Enabled2_20250520:cache_size/2,SharedHighlightingIphDesktop.Enabled:availability/any/event_1/name%3Aiph_desktop_shared_highlighting_trigger%3Bcomparator%3A%3D%3D0%3Bwindow%3A7%3Bstorage%3A360/event_trigger/name%3Aiph_desktop_shared_highlighting_trigger%3Bcomparator%3A%3C5%3Bwindow%3A360%3Bstorage%3A360/event_used/name%3Aiph_desktop_shared_highlighting_used%3Bcomparator%3A%3C2%3Bwindow%3A360%3Bstorage%3A360/session_rate/any,SharedTabGroups.DataSharingEnabled:show_send_feedback/true,SidePanelCompanionDesktopM116Plus.EnableCompanionChromeOS_20240222:open-companion-for-image-search/false/open-companion-for-web-search/false/open-contextual-lens-panel/false/open-links-in-current-tab/false,SideSearchInProductHelp.Enabled:availability/any/event_trigger/name%3Aside_search_iph_tgr%3Bcomparator%3A%3D%3D0%3Bwindow%3A90%3Bstorage%3A360/event_used/name%3Aside_search_opened%3Bcomparator%3A%3D%3D0%3Bwindow%3A90%3Bstorage%3A360/session_rate/%3C3,SkiaGraphite.Enabled:dawn_backend_validation/false/dawn_skip_validation/true,SpeculativeServiceWorkerWarmUp.Enabled_05m_05c_20250214:sw_warm_up_duration/5m/sw_warm_up_max_count/5/sw_warm_up_on_idle_timeout/false/sw_warm_up_request_queue_length/100,SuppressesNetworkActivitiesOnSlowNetwork.SuppressesAll:slow_network_threshold/208ms/slow_network_threshold_for_prerendering/208ms/slow_network_threshold_for_search_prefetch/208ms,SyncIncreaseNudgeDelayForSingleClient.EnabledFactor2:SyncIncreaseNudgeDelayForSingleClientFactor/2%2E0,TabAudioMuting.Enabled:availability/any/event_trigger/name%3Atab_audio_muting_iph_triggered%3Bcomparator%3A%3D%3D0%3Bwindow%3A120%3Bstorage%3A365/event_used/name%3Atab_audio_muting_toggle_viewed%3Bcomparator%3A%3D%3D0%3Bwindow%3A120%3Bstorage%3A365/session_rate/%3D%3D0,TabSearchInProductHelp.TabSearchIPH:availability/any/event_trigger/name%3Atab_search_iph_tgr%3Bcomparator%3A%3D%3D0%3Bwindow%3A90%3Bstorage%3A360/event_used/name%3Atab_search_opened%3Bcomparator%3A%3D%3D0%3Bwindow%3A90%3Bstorage%3A360/session_rate/%3C3,TabstripComboButton.Enabled:tab_search_toolbar_button/true,ThreadCacheMinCachedMemoryForPurging.size_500kb_20230909:ThreadCacheMinCachedMemoryForPurgingBytes/512000,ThreadCachePurgeInterval.interval_1_2_60_20230909:ThreadCacheDefaultPurgeInterval/2s/ThreadCacheMaxPurgeInterval/60s/ThreadCacheMinPurgeInterval/1s,TraceSiteInstanceGetProcessCreation.EnabledAndCrash:crash_on_creation/true,TrustSafetySentimentSurvey.Enabled:max-time-to-prompt/60m/min-time-to-prompt/2m/ntp-visits-max-range/4/ntp-visits-min-range/2/privacy-sandbox-3-consent-accept-probability/0%2E2/privacy-sandbox-3-consent-accept-trigger-id/5t9KNsR4e0ugnJ3q1cK0RPfRpsbm/privacy-sandbox-3-consent-decline-probability/0%2E2/privacy-sandbox-3-consent-decline-trigger-id/P5svv2BbH0ugnJ3q1cK0YhTWZkiM/privacy-sandbox-3-notice-dismiss-probability/0%2E2/privacy-sandbox-3-notice-dismiss-trigger-id/2gMg6iHpn0ugnJ3q1cK0XyL2C2EX/privacy-sandbox-3-notice-ok-probability/0%2E2/privacy-sandbox-3-notice-ok-trigger-id/vBraRD9GZ0ugnJ3q1cK0T1owvGGa/privacy-sandbox-3-notice-settings-probability/0%2E2/privacy-sandbox-3-notice-settings-trigger-id/WZpnNehvi0ugnJ3q1cK0Nsdcf1Vf/privacy-settings-probability/0%2E0/probability/1%2E0/transactions-probability/0%2E0/trusted-surface-probability/0%2E0,TrustSafetySentimentSurveyV2.Enabled_20240212:browsing-data-probability/0%2E0/browsing-data-trigger-id/1iSgej9Tq0ugnJ3q1cK0QwXZ12oo/control-group-probability/0%2E0/control-group-trigger-id/CXMbsBddw0ugnJ3q1cK0QJM1Hu8m/download-warning-ui-probability/0%2E0/download-warning-ui-trigger-id/7SS4sg4oR0ugnJ3q1cK0TNvCvd8U/max-time-to-prompt/60m/min-session-time/30s/min-time-to-prompt/2m/ntp-visits-max-range/4/ntp-visits-min-range/2/password-check-probability/0%2E0/password-check-trigger-id/Xd54YDVNJ0ugnJ3q1cK0UYBRruNH/password-protection-ui-probability/0%2E0/password-protection-ui-trigger-id/bQBRghu5w0ugnJ3q1cK0RrqdqVRP/privacy-guide-probability/0%2E0/privacy-guide-trigger-id/tqR1rjeDu0ugnJ3q1cK0P9yJEq7Z/probability/0%2E0/safe-browsing-interstitial-probability/0%2E0/safe-browsing-interstitial-trigger-id/Z9pSWP53n0ugnJ3q1cK0Y6YkGRpU/safety-check-probability/0%2E0/safety-check-trigger-id/YSDfPVMnX0ugnJ3q1cK0RxEhwkay/safety-hub-interaction-probability/0%2E0/safety-hub-interaction-trigger-id/TZq2S4frt0ugnJ3q1cK0Q5Yd4YJM/safety-hub-notification-probability/0%2E0/safety-hub-notification-trigger-id/kJV17f8Lv0ugnJ3q1cK0Nptk37ct/trusted-surface-probability/0%2E0/trusted-surface-time/5s/trusted-surface-trigger-id/CMniDmzgE0ugnJ3q1cK0U6PaEn1f,TrustTokenOriginTrial.Enabled:TrustTokenOperationsRequiringOriginTrial/all-operations-require-origin-trial,UMA-NonUniformity-Trial-1-Percent.group_01:delta/0%2E01,UMA-Pseudo-Metrics-Effect-Injection-25-Percent.BigEffect_01:multiplicative_factor/1%2E05,UkmSamplingRate.Sampled_NoSeed_Other:_default_sampling/1,UnoDesktopHistorySyncPillExperiment.EnabledWithBrowseAcrossDevices:history-sync-optin-expansion-pill-option/browse-across-devices,UserBypassUI.Enabled:expiration/90d,V8CodeFlushing.Time180Sparkplug:V8FlushCodeOldTime/180,V8EfficiencyModeTiering.Delay10k:V8EfficiencyModeTieringDelayTurbofan/10000,VSyncAlignedPresent.Enabled_for_all_frames:Target/Interaction,VerifyDidCommitParams.Enabled:gesture/true/http_status_code/true/intended_as_new_entry/true/is_overriding_user_agent/true/method/true/origin/true/post_id/true/should_replace_current_entry/true/should_update_history/true/url/true/url_is_unreachable/true,VisitedURLRankingService.VisitedURLRankingService:VisitedURLRankingFetchDurationInHoursParam/168,WaitForLateScrollEvents.DeadlineRatio0_5:deadline_ratio/0%2E5,WebRTC-ZeroPlayoutDelay.min_pacing%3A0ms%2Cmax_decode_queue_size%3A8%2C:max_post_decode_queue_size/10/reduce_steady_state_queue_size_threshold/20,WhatsNewHats.Enabled_en_20241016:en_site_id/6bnVh68QF0ugnJ3q1cK0NQxjpCFS/probability/0%2E4,WhatsNewSparkEdition.Enabled_benefits:whats_new_customization/BENEFITS/whats_new_survey_id/PsSZ5E6nP0ugnJ3q1cK0WcE1Zf8H,ZPSPrefetchDebouncingDesktop.Enabled_300ms_FromLastRun:ZeroSuggestPrefetchDebounceDelay/300/ZeroSuggestPrefetchDebounceFromLastRun/true", "force-fieldtrials": "ANGLEPerContextBlobCache/Enabled/AVFoundationCaptureForwardSampleTimestamps/Disabled/AXBlockFlowIterator/Enabled/AXRandomizedStressTests/Enabled/AXTreeFixing/Enabled/*AccessibilityPerformanceMeasurementExperiment/Control/AccessibilitySerializationSizeMetrics/Enabled/*AggressiveShaderCacheLimits/Enabled/AiSettingsPageEnterpriseDisabledUi/Enabled/AllowDatapipeDrainedAsBytesConsumerInBFCache/Enabled/AlwaysBlock3pcsIncognito/Enabled/AnimationForDesktopCapturePermissionChecker/Enabled/AsyncQuicSession/Enabled/AttributionReportingInBrowserMigration/Enabled/AudioInputConfirmReadsViaShmem/Enabled/AutoDisableAccessibility/Enabled/AutoPictureInPictureForVideoPlayback/Enabled/AutoSpeculationRules/Enabled_20231201/AutocompleteControllerMetricsOptimization/Enabled/AutofillAddressSuggestionsOnTyping/Enabled/AutofillAddressUserPerceptionSurveyUS/Enabled/AutofillAiTeamfoodInternal/Enabled/AutofillAiTeamfoodV2/Enabled/AutofillAiVoteForFormatStrings/Enabled/AutofillBetterLocalHeuristicPlaceholderSupport/Enabled/AutofillCreditCardUserPerceptionSurvey/Enabled/AutofillDeduplicateAccountAddresses/Enabled/AutofillEnableBuyNowPayLaterDesktop/Enabled/AutofillEnableCardBenefitsForBmo/Enabled/AutofillEnableCardBenefitsIph/Enabled/AutofillEnableExpirationDateImprovements/Enabled/AutofillEnableFillingPhoneCountryCodesByAddressCountryCodes/Enabled/AutofillEnableFpanRiskBasedAuthentication/Enabled/AutofillEnableLabelPrecedenceForTurkishAddresses/Enabled/AutofillEnableLogFormEventsToAllParsedFormTypes/Enabled/AutofillEnableNewCardProfileRankingAlgorithm/Enabled/AutofillEnableSupportForParsingWithSharedLabels/Enabled/AutofillFixSplitCreditCardImport/Enabled/AutofillGreekRegexes/Enabled/AutofillI18nINAddressModel/Enabled/AutofillImproveAddressFieldSwapping/Enabled/AutofillImproveCityFieldClassification/Enabled/AutofillImproveSubmissionDetectionV2/Enabled/AutofillImprovedLabels/Enabled_WithDifferentiatingLabelsInFront/AutofillModelPredictions/Enabled/AutofillOptimizeFormExtraction/Enabled/AutofillPageLanguageDetection/Enabled/AutofillPasswordUserPerceptionSurvey/Enabled/AutofillPaymentsFieldSwapping/Enabled/AutofillPopupDontAcceptNonVisibleEnoughSuggestion/Enabled/AutofillPopupZOrderSecuritySurface_V2/Enabled/AutofillRelaxAddressImport/Enabled/AutofillSharedStorageServerCardData/Enabled/AutofillStructuredFieldsDisableAddressLines/Enabled/AutofillSupportLastNamePrefix/Enabled/AutofillSupportPhoneticNameForJP/Enabled/AutofillSurveys/Card_20230606/AutofillUKMExperimentalFields/Enabled/AutofillUnifyRationalizationAndSectioningOrder/Enabled/AutofillUnmaskCardRequestTimeout/Enabled/AutofillUpstream/Enabled_20220124/AutofillVcnEnrollStrikeExpiryTime/Enabled/AvoidDuplicateDelayBeginFrame/Enabled/AvoidEntryCreationForNoStore/Enabled/AvoidUnnecessaryBeforeUnloadCheckSync/WithSendBeforeUnload/AvoidUnnecessaryForcedLayoutMeasurements/Enabled/BackForwardCacheNonStickyDoubleFix/Holdback/BackForwardCacheNotRestoredReasons/Enabled/BackForwardCachePrioritizedEntry/Enabled_20250327/BackNavigationMenuIPH/EnabledIPHWhenUserPerformsChainedBackNavigation_20230510/BackgroundResourceFetch/Enabled/*BatchNativeEventsInMessagePumpKqueue/Enabled/BatterySaverModeAlignWakeUps/Enabled/*BeaconLeakageLogging/Enabled_v1/BlinkLifecycleScriptForbidden/Enabled/BlockAcceptClientHints/Enabled/BookmarksUseBinaryTreeInTitledUrlIndex/Enabled/BoostRenderProcessForLoading/Enabled/BoundaryEventDispatchTracksNodeRemoval/Enabled/BrowserInitiatedAutomaticPictureInPicture/Enabled/BrowserSignalsReportingEnabled/Enabled/*BrowserThreadPoolAdjustmentForDesktop/thread_pool_default_20230920/BubbleMetricsApi/Enabled/CSSReadingFlow/Enabled/CacheSharingForPervasiveScripts/Enabled_20250520/CacheStorageTaskPriority/Enabled/CameraMicPreview/CameraOther_20250408/Canvas2DAutoFlushParams/Candidate/Canvas2DReclaimUnusedResources/Enabled/CanvasHibernationExperiments/Enabled/CanvasHibernationSnapshotZstd/Enabled/CanvasNoise/Enabled/CanvasTextNg/Enabled/CastStreamingHardwareHevc/Enabled/CastStreamingMediaVideoEncoder/Enabled/CastStreamingVp9/Enabled/*CatanCombinedHoldback23H2/Enabled/CheckHTMLParserBudgetLessOften/Enabled_Nonstable_20230320/ChromeCompose/Enabled/ChromeWallpaperSearchGlobal/WallpaperSearchGlobal/ChromeWallpaperSearchHaTS/Enabled/ChromeWallpaperSearchLaunch/DefaultOnLaunched/ChromeWebStoreNavigationThrottle/Enabled/ChromeWideEchoCancellation/Enabled_20220412/ChromnientFetchSrp/FetchSrpEnabled/ChromnientLatencyOptimizations/AllOptimizationsEnabled/ChromnientMoreTranslateLanguages/MoreTranslateLanguagesEnabled/ChromnientNewFeedback/NewFeedbackEnabled/ChromnientPostLaunchTranslate/TranslateButtonEnabled/ChromnientSidePanelOpenInNewTab/Enabled/ChromnientSimplifiedSelection/SimplifiedSelectionEnabled/ChromnientSurvey/Enabled/ClearGrShaderDiskCacheOnInvalidPrefix/Enabled/ClickToCapturedPointer/Enabled/ClientSideDetectionAcceptHCAllowlist/Enabled/ClientSideDetectionLlamaForcedTriggerInfoForScamDetection/Enabled/ClientSideDetectionRetryLimit/Enabled/ClientSideDetectionSamplePing/Enabled/ClientSideDetectionSendLlamaForcedTriggerInfo/Enabled/CloneDevToolsConnectionOnlyIfRequested/Enabled/CoalesceSelectionchangeEvent/Enabled/CodeBasedRBD/Enabled_20230420/CommerceLocalPDPDetection/Enabled/CommercePriceInsights/Enabled/Compare/Enabled_Dogfood/ComposeAXSnapshot/Enabled/ComposeAcceptanceSurvey/Enabled/ComposeCloseSurvey/Enabled/ComposeModelQualityLogging/ComposeLoggingEnabled_Dogfood/ComposeOnDeviceModel/Enabled/ComposeProactiveNudgePosition/Enabled_CursorNudgeModel/ComposeV3Migration/Enabled/CompositeBackgroundColorAnimation/Enabled/CompositeClipPathAnimation/Enabled/CompositorLoadingAnimations/Enabled/CompressionDictionaryPreload/EnabledPreloadConditionalUse/CompressionDictionaryTransportRequireKnownRootCert/Disable/ConditionalImageResize/Enabled/ConfigurableV8CodeCacheHotHours/cache_72h_20230904/ContextualSearchBox/Enabled_NoAutoFocus_20250421/CookieDeprecationFacilitatedTestingCookieDeprecation/Treatment_PreStable_20231002/CookieDeprecationFacilitatedTestingFledgeTrustedSignalsHeaders/Enabled/CookieSameSiteConsidersRedirectChainDesktop/Enabled/CreateURLLoaderPipeAsync/Enabled/CustomizableSelect/Enabled/CustomizeChromeSidePanelExtensionsCard/Enabled/DOMStorageReliabilityEnhancements/Enabled_20240904/DTCKeyRotationUploadedBySharedAPIEnabled/Enabled/DecommitPooledPages/Enabled/DefaultProfileEnterpriseBadging/Enabled/DeferSpeculativeRFHCreation/EnabledWithPrewarmAndDelay/DeprecateUnload/Enabled_135/DesktopCapturePermissionCheckerPreMacos14_4/Enabled/DesktopNtpDriveCache/Cache_1m/DesktopNtpImageErrorDetection/ImageErrorDetection/DesktopNtpMiddleSlotPromoDismissal/MiddleSlotPromoDismissal/DesktopNtpMobilePromo/Enabled_20241101/DesktopNtpModules/RecipeTasksRuleBasedDiscountDriveManagedUsersCartOptimizeRecipeTasksSAPIV2Fre_Enabled/DesktopNtpOneGoogleBarAsyncBarParts/Enabled/DesktopNtpTabResumption/TabResumption_Random/DesktopOmniboxCalculatorProvider/Enabled/DesktopOmniboxRichAutocompletionMinChar/Enabled_1_v1/DesktopOmniboxShortcutBoost/Enabled/DesktopOmniboxStarterPackExpansion/Enabled/DesktopOmnibox_HistoryQuickProviderSpecificity/Enabled/DesktopPWAInstallPromotionML/Disabled/DestroySystemProfiles/DestroySystemProfiles/DeviceBoundSessionAccessObserverSharedRemote/Enable/DirectCompositorThreadIpcMacLinuxChromeOS/Enabled/DisableCompressParkableStrings/Disabled/DisableGles2ForOopR/Enabled/*DisableMojoTaskPerMessage/Enabled/DisableUrgentPageDiscarding/Disabled/DiscardInputEventsToRecentlyMovedFrames/DoNotDiscard/DiscountAutoPopup/Enabled/DiscountConsentV2/enabled_ntp_native_dialog_with_approved_strings_M104_20220809/DlpRegionalizedEndpoints/Enabled/DnsHttpsSvcbTimeout/Enabled/DoNotEvictOnAXLocationChange/Enabled/DownloadWarningSurvey/DownloadBubbleBypass_20240513/*DwaFeature/Enabled/EagerPrefetchBlockUntilHeadDifferentTimeoutsRetrospective/Enabled/EnableAsyncUploadAfterVerdict/Enabled/*EnableConfigurableThreadCacheMultiplier/multiplier_2.0_20230904/EnableDiscountOnShoppyPagesDesktop/Enabled/EnableExtensionsExplicitBrowserSignin/Enabled/*EnableHangWatcher/Enabled/EnableLazyLoadImageForInvisiblePage/Enabled_AllInvisiblePage/EnablePDPMetricsUSDesktopIOS/Enabled/EnablePolicyPromotionBanner/Enabled/EnablePrintWatermark/Disabled/EnableTLS13EarlyData/Enabled/EnterpriseFileObfuscation/Enabled/EnterpriseFileSystemAccessDeepScan/Enabled/EnterpriseUpdatedProfileCreationScreen/Enabled/EsbAsASyncedSetting/Enabled/EscapeLtGtInAttributes/Enabled/EventTimingIgnorePresentationTimeFromUnexpectedFrameSource/Enabled/ExportFrameTimingAfterFrameDoneExperiment/Enabled/ExtendedReportingRemovePrefDependency/EnabledRemovePrefDependency/ExtensionManifestV2Deprecation/Enabled_MV2ExtensionsUnsupported/ExtensionsToolbarAndMenuRedesign/Enabled/ExtensionsZeroStatePromo/Variant1/*ExtremeLightweightUAFDetector/Quarantine_900_100_512_v6/FLEDGEBiddingAndAuctionServer/Enabled/FastPathNoRaster/Enabled/FasterSetCookie/AsyncGetCookiesOnSet/FedCmIntrusionMitigation/CooldownOnIgnore/FedCmSegmentationPlatform/SegmentationPlatform/FeedbackIncludeVariations/Enabled/FencedFramesEnableCredentialsForAutomaticBeacons/Enabled/FencedFramesEnableCrossOriginAutomaticBeaconData/Enabled/FencedFramesEnableReportEventHeaderChanges/Enabled/FencedFramesEnableSrcPermissionsPolicy/Enabled/FenderAutoPreconnectLcpOrigins/EnabledWithOne_20240214/FetchLaterAPI/Enabled_20240112/FieldRankServerClassification/FieldRankServerClassification_Experiment_20250422/FingerprintingProtectionFilter/Enabled_20250331/FingerprintingProtectionFilterIncognito/Enabled_20250416/FledgeBiddingAndAuctionNonceSupport/Enabled/FledgeModifyInterestGroupPolicyCheckOnOwner/Enabled/*FlushPersistentSystemProfileOnWrite/Enabled/FormControlsVerticalWritingModeDirectionSupport/Enabled/FrameRoutingCache/Enabled/FreezingOnBatterySaver/Enabled/GCMUseDedicatedNetworkThread/Enabled/GCOnArrayBufferAllocationFailure/Enabled/GlicContextualCueingDogfood/Enabled/*GlicDogfood/Enabled/GlicRolloutDogfood/Enabled/GlicSettingsDogfood/Enabled/GlicTieredRollout/Enabled/GlicUserStatusCheckDogfood/Enabled/GlicZeroStateSuggestionsDogfood/Enabled/GoogleLensDesktopImageFormatOptimizations/WebpQualityBackendV6/GpuYieldRasterization/Enabled/GrCacheLimits/cache_96_256_8_default_20230911/GroupedHistoryAllLocales/Enabled/*GwpAsan2024WinMac/CombinedGreedyArmForVariationsPresubmit/HTTP2/Enabled6/HangoutsExtensionV3/Enabled/HappinessTrackingSurveysForHistoryEmbeddings/Enabled/*HappyEyeballsV3/Enabled/*HeapProfilingLoadFactor/Enabled/HideDelegatedFrameHostMac/Enabled/HistoryEmbeddingsV2Images/Enabled/HstsTopLevelNavigationsOnly/Enabled/HttpCacheNoVarySearch/Enabled_NVS_Omnibox_Prefetch/HttpDiskCachePrewarming/WithReadAndDiscardBody_20240328/HttpsFirstBalancedModeAutoEnable/Enabled/HttpsFirstModeV2ForTypicallySecureUsers/Enabled/IPProtectionMdlImpl/enabled/*IPProtectionPhase0/enabled_baseline_2025-03-19_Stable_Perf_External/IdbPrioritizeForegroundClients/Enabled/IgnoreDiscardAttemptMarker/Enabled/ImageDescriptionsAlternateRouting/Enabled/ImprovedFailedLoginDetectionStudy/Enabled/IncreaseCookieAccessCacheSize/Enabled/IncreasedCmdBufferParseSlice/Enabled/InlineFullscreenPerfExperiment/Enabled/KeyboardFocusableScrollers/Enabled/KeyboardLockPrompt/Enabled_PEPC/LCPPDeferUnusedPreload/EnableWithPostTask_20240426/LCPPFontURLPredictor/Enabled/LCPPImageLoadingPriority/MediumPriority_20240418/LCPPLazyLoadImagePreload/EnableWithNativeLazyLoading_20231113/LCPPPrefetchSubresource/Enabled/LCPTimingPredictorPrerender2/Enabled/LanguageDetectionAPI/Enabled/LazyBlinkTimezoneInit/Enabled/LazyUpdateTranslateModel/Enabled/LegacyKeyRepeatSynthesis/Disabled/LensSearchSidePanelScrollToAPI/LensSearchSidePanelScrollToAPIEnabled/LinkPreview/EnabledAltClick/ListAccountsUsesBinaryFormat/Enabled/LiveCaptionExperimentalLanguages/Enabled/LiveCaptionMultiLanguageRollout/Enabled/LoadingPhaseBufferTimeAfterFirstMeaningfulPaint/buffer_500ms_20230907/LoadingPredictorLimitPreconnectSocketCount/Enabled/LocalIpAddressInEvents/Enabled/LocalNetworkAccessChecks/EnabledWarning/LocalWebApprovalsLinuxMacWindows/Enabled/LogOnDeviceMetricsOnStartup/Enabled/LongAnimationFrameSourceCharPosition/Enabled/LowPriorityAsyncScriptExecution/Enabled/MHTML_Improvements/Enabled/MacAccessibilityAPIMigration/Enabled/MacICloudKeychainRecoveryFactor/Enabled/MacKeychainApiMigration/Enabled/*MainNodeAnnotationsRollout/Enabled/ManagedProfileRequiredInterstitial/Enabled/MediaDeviceIdStoragePartitioning/Enabled/MemoryCacheStrongReference/FilterImageAllPages_20240124/MemoryPurgeInBackground/DisableAll/MemorySaverModeRenderTuning/Enabled/MerchantTrust/Enabled_20250130/*MetricsLogTrimming/Enabled/MigrateDefaultChromeAppToWebAppsGSuite/Enabled_20210111/MigrateDefaultChromeAppToWebAppsNonGSuite/Enabled_20210111/MojoChannelAssociatedSendUsesRunOrPostTask/Enabled/*MojoInlineMessagePayloads/Enabled/MojoPredictiveAllocation/Enabled/MsaaSettingsMac/DetectHiDpiForMsaa/MultiBufferNeverDefer/Enabled/MultipleSpareRPHs/Enabled2/MutationEvents/Disabled/NetworkQualityEstimatorParameterTuning/Experiment/NewContentForCheckerboardedScrolls/Enabled/NoPasswordSuggestionFiltering/Enabled/NoThrottlingVisibleAgent/Enabled/NoThrowForCSPBlockedWorker/Enabled/NonStandardAppearanceValueSliderVertical/Disabled/NotificationTelemetryService/Enabled/NtpMicrosoftFilesCard/Enabled_NonInsights/OcclusionCullingQuadSplitLimit/quad_split_limit_8/OidcAuthProfileManagement/Enabled/OmitBlurEventOnElementRemoval/Enabled/OmniboxBundledExperimentV1/DesktopExperiments/OmniboxDriveEligibility/EnabledPrimaryAccountStrictEligibilityAndNoSyncRequirement/OmniboxLogURLScoringSignals/Enabled/OmniboxOnDeviceBrainModel/Enabled/OmniboxOnDeviceHeadModelSelectionFix/Fix/OmniboxOnFocusZPSV1/Enabled_6_Suggestions/OmniboxStarterPackIPH/Enabled/OptGuideBatchSRPTuning/Enabled_20240624/OptimizeHTMLElementUrls/Enabled/OutOfProcessPrintDriversPrint/Enabled_20230912/PWANavigationCapturingV2WindowMacLinux/EnabledSettingOnByDefault20241105/PageActionsMigration/Enabled/PageInfoAboutThisSite40Langs/Enabled_231129/PaintHoldingOOPIF/Enabled/ParkableStringsLessAggressiveAndZstd/Enabled/*PartialPageZeroing/EnabledPAAndV8_20241106/*PartitionAllocBackupRefPtr/Enabled/*PartitionAllocFewerMemoryRegions/Enabled/PartitionAllocLargeThreadCacheSizeDesktop/size_32768_20230925/*PartitionAllocMemoryReclaimer/Interval_8sec/PartitionAllocShortMemoryReclaim/Enabled/*PartitionAllocUnretainedDanglingPtr/Enabled/*PartitionAllocWithAdvancedChecks/Enabled/PartitionNetworkStateByNetworkAnonymizationKey/EnableFeatureForTests/*PassHistogramSharedMemoryOnLaunch/Enabled/PassageEmbeddingsPerformance/Disabled/PasswordFormClientsideClassifier/Enabled/PasswordFormGroupedAffiliations/Enabled/Path2DPaintCache/Enabled/PdfInfoBar/EnabledPdfLoad/PdfInkSignatures/Enabled/PdfUseShowSaveFilePicker/Enabled/PdfUseSkiaRenderer/Enabled/*PerfCombined2024/Enabled/PerformanceControlsHatsStudy/EnabledHighEfficiencyOptOut_20230223/PerformanceControlsPPMSurvey/Enabled_Uniform_20250526/PerformanceInterventionAlgorithm/EnabledLessAggressive_20250428/PermissionElementPromptPositioning/NearElement/PermissionsAIv1/Enabled/PermissionsAIv3/Enabled/PermissionsAIv3Geolocation/Enabled/PlusAddressAcceptedFirstTimeCreateSurvey/Enabled/PlusAddressDeclinedFirstTimeCreateSurvey/Enabled/PlusAddressFilledPlusAddressViaManualFallbackSurvey/Enabled/PlusAddressFullFormFill/Enabled/PlusAddressSuggestionsOnUsernameFields/Enabled/PlusAddressUserCreatedMultiplePlusAddressesSurvey/Enabled/PlusAddressUserCreatedPlusAddressViaManualFallbackSurvey/Enabled/PlusAddressUserDidChooseEmailOverPlusAddressSurvey/Enabled/PlusAddressUserDidChoosePlusAddressOverEmailSurvey/Enabled/PlusAddressesExperiment/Enabled/PlzDedicatedWorker/Enabled/PolicyBlocklistProceedUntilResponse/Holdback/PowerBookmarkBackend/Enabled/*PreconnectFromKeyedService/EnabledWithOTRtrue_20250319/PreconnectToSearchDesktop/EnabledWithStartupDelayForegroundOnly/PrefetchProxyDesktop/Enabled/PrefetchReusable/Enabled_20250129/PrefetchScheduler/Enabled/PrefetchServiceWorker/Enabled/PrefetchServiceWorkerNoFetchHandlerFix/Enabled/PreloadInlineDeferredImages/Enabled_Preload_20250402/PreloadTopChromeWebUILessNavigations/Enabled/PreloadingNoSamePageFragmentAnchorTracking/Enabled/Prerender2BookmarkBarTriggerV2/MouseDownAndMouseHover300ms_20241008/Prerender2EarlyDocumentLifecycleUpdateV2/Enabled/Prerender2FallbackPrefetchSpecRules/Enabled_NoTimeout_Burst/Prerender2NewTabPageTriggerV2/BothMouseDownAndMouseHover_20241010/PreserveDiscardableImageMapQuality/Enabled/PriceTrackingDesktopExpansionStudy/Enabled/PriceTrackingPageActionIconLabelFeatureIPH/Enabled/PrivacyGuideAiSettings/Enabled/*PrivacySandboxAdsAPIs/Enabled_Notice_M1_AllAPIs_Expanded_NoOT_Stable/PrivacySandboxAdsApiUxEnhancements/Enabled/PrivacySandboxAllowPromptForBlocked3PCookies/Enabled/PrivacySandboxAttestationsDefaultDeny/Disabled/PrivacySandboxInternalsDevUI/Enabled_Dogfood/PrivacySandboxMigratePrefsToSchemaV2/Enabled/PrivacySandboxNoticeQueue/Enabled/PrivacySandboxPrivacyGuideAdTopics/Enabled/PrivacySandboxRelatedWebsiteSetsUi/Enabled/PrivacySandboxSentimentSurvey/Enabled/PrivateAggregationApiErrorReporting/Enabled/PrivateStateTokens/Enabled/ProcessHtmlDataImmediately/AllChunks/ProcessIsolationForFencedFrames/Enabled/ProfileRemoteCommands/Enabled/ProfileSignalsReportingEnabled/Enabled/ProfilesReordering/Enabled/ProgrammaticScrollAnimationOverride/Enabled/ProgressiveAccessibility/Enabled/ProtectedAudienceAuctionDownloaderStaleWhileRevalidate/Enabled/ProtectedAudienceAuctionTextConversionHelpers/Enabled/ProtectedAudienceBidderUseBalancingThreadSelector/Enabled/ProtectedAudienceClickiness/Enabled/ProtectedAudienceDealsSupport/Enabled/ProtectedAudienceEagerJSCompilation/Enabled/ProtectedAudienceEarlyProcessCreationStudy/Enabled/ProtectedAudienceKAnonymityKeyCacheStudy/Enabled/ProtectedAudienceMultiThreadedSellerWorklet/TwoThreads/ProtectedAudienceNoWasmLazyCompilationStudy/Enabled/ProtectedAudiencePreconnectCacheStudy/Enabled/ProtectedAudiencePrepareBidderContextsStudy/Enabled/ProtectedAudienceSellerSignalsRequestsOneAtATime/Enabled/ProtectedAudienceSendDebugReportCooldownsToBandA/Enabled/ProtectedAudienceTrustedSignalsKVv1CreativeScanning/Enabled/ProtectedAudiencesEnableBandAKAnonEnforcement/Enabled/ProtectedAudiencesEnableSampleDebugReportOnCookieSetting/Enabled/ProtectedAudiencesHeaderDirectFromSellerSignalsStudy/Enabled/ProtectedAudiencesKAnonymityEnforcementStudy/Enabled/ProtectedAudiencesUpdateIfOlderThanMs/Enabled/PruneOldTransferCacheEntries/Enabled/PsDualWritePrefsToNoticeStorage/Enabled/PushMessagingDisallowSenderIDs/Enabled/PushMessagingGcmEndpointEnvironment/Enabled_Dogfood/QUIC/Enabled/RTCAlignReceivedEncodedVideoTransforms/Enabled/ReadAnythingDocsIntegrationRollout/Enabled/ReadAnythingIPHRollout/Enabled/ReadAnythingPermanentAccessibility/Enabled/*ReadAnythingReadAloudDesktop/Enabled/*ReadAnythingReadAloudPhraseHighlighting/Enabled/ReclaimOldPrepaintTiles/Enabled/ReclaimPrepaintTilesWhenIdle/Enabled/RedWarningSurvey/RedInterstitial_20241212/ReduceAcceptLanguage/Enabled/ReduceAcceptLanguageHTTP/Enabled/*ReduceIPAddressChangeNotification/Enabled/ReduceIPCCombined/EnabledUkmReduceAddEntryIPC/RemotePageMetadataDesktopExpansion/Enabled_20240514/RemoveCancelledScriptedIdleTasks/Enabled/RemoveDataUrlInSvgUse/Enabled/RemoveRendererProcessLimit/Enabled/RenderBlockingFullFrameRate/Enabled/RenderDocumentWithNavigationQueueing/EnabledSubframeWithQueueing/RendererSideContentDecoding/Enabled/RenderingOptimizationsHoldback/Enabled/ReportingServiceAlwaysFlush/Enabled/ResolutionBasedDecoderPriority/Enabled/RetryGetVideoCaptureDeviceInfos/Enabled/RustyPng/RustyPngExperimentForTests/SRIMessageSignatureEnforcement/Enabled/SafeBrowsingDailyPhishingReportsLimit/Enabled/SafeBrowsingExternalAppRedirectTelemetry/Enabled/SafeBrowsingRemoveCookiesInAuthRequests/Enabled/SafetyCheckUnusedSitePermissions/Enabled/SafetyHub/Enabled/SafetyHubDisruptiveNotificationRevocation/ShadowRun_20250227/SafetyHubOneOffHats/SafetyHub_NoNotification/SavedTabGroupUrlRestriction/Enabled/ScreenCaptureKitMacScreen/Enabled/ScrimForBrowserWindowModal/Enabled/ScrimForTabModal/Enabled/SeamlessRenderFrameSwap/Enabled/SearchEngineChoiceClearInvalidPref/Enabled/SearchEnginePreconnect2/EnabledWithbase_60_30_30_30__20250507/SearchEnginePreconnectInterval/EnabledWith50_20250114/*SearchPrefetchHighPriorityPrefetches/EnabledHighPriorityBothTriggers_20230721/SecurityPageHats/Enabled/SegmentationPlatformUmaFromSqlDb/Enabled/SendTabToSelfIOSPushNotifications/Enabled/ServiceWorkerAutoPreload/Enabled/ServiceWorkerBackgroundUpdateForRegisteredStorageKeys/ServiceWorkerBackgroundUpdateForRegisteredStorageKeys/ServiceWorkerStaticRouterRaceNetworkRequestPerformanceImprovement/Enabled/SettingSearchExplorationHaTS/Enabled/SharedDictionaryCache/Enabled2_20250520/SharedHighlightingIphDesktop/Enabled/SharedTabGroups/DataSharingEnabled/SharedWorkerBlobURLFix/Enabled/SharingDisableVapid/Enabled/SharingHubDesktopScreenshots/Enabled/ShowSuggestionsOnAutofocus/Enabled/SidePanelCompanionDesktopM116Plus/EnableCompanionChromeOS_20240222/SidePanelPinningWithResponsiveToolbar/EnabledWithSidePanelPinning/SideSearchInProductHelp/Enabled/SimdutfBase64Support/Enabled_SimdutfWithFurtherOptimization/SimpleCachePrioritizedCaching/Enabled/SingleVideoFrameRateThrottling/Enabled/SiteInstanceGroupsForDataUrls/Enabled/*SkiaGraphite/Enabled/SkipPagehideInCommitForDSENavigation/Enabled/SonomaAccessibilityActivationRefinements/Enabled/SpdyHeadersToHttpResponseUseBuilder/Enabled/SpeculativeFixForServiceWorkerDataInDidStartServiceWorkerContext/Enabled/SpeculativeImageDecodes/Enabled/SpeculativeServiceWorkerWarmUp/Enabled_05m_05c_20250214/SplitCacheByNetworkIsolationKey/EnableFeatureForTests/SqlFixedMmapSize/Enabled/SqlScopedTransactionWebDatabase/Enabled/SqlWalMode/Enabled/StandardizedBrowserZoom/Enabled/StandardizedTimerClamping/Enabled/StorageBuckets/Enabled/StreamlineRendererInit/Enabled/SuppressesNetworkActivitiesOnSlowNetwork/SuppressesAll/*SwiftShaderDeprecation/SwiftShaderDisabled/SyncIncreaseNudgeDelayForSingleClient/EnabledFactor2/SyncPointGraphValidation/Enabled/TabAudioMuting/Enabled/TabCaptureInfobarLinks/Enabled/TabGroupShortcuts/Enabled/TabGroupSyncServiceDesktopMigration/Enabled/TabGroupsCollapseFreezing/Disabled/TabHoverCardImagesMacArm/Enabled/TabSearchInProductHelp/TabSearchIPH/*TabstripComboButton/Enabled/TabstripDeclutter/Enabled/TailoredSecurityIntegration/TailoredSecurityIntegration/TaskManagerDesktopRefresh/TaskManagerDesktopRefresh/TextInputHostMojoCapabilityControlWorkaround/Enabled/TextSafetyScanLanguageDetection/Enabled/*ThreadCacheMinCachedMemoryForPurging/size_500kb_20230909/*ThreadCachePurgeInterval/interval_1_2_60_20230909/ThrottleUnimportantFrameTimers/Enabled/ToolbarPinning/Enabled/TraceSiteInstanceGetProcessCreation/EnabledAndCrash/TrackingProtection3pcd/Enabled/TranslateBubbleOpenSettings/TranslateBubbleOpenSettings/TrustSafetySentimentSurvey/Enabled/TrustSafetySentimentSurveyV2/Enabled_20240212/TrustTokenOriginTrial/Enabled/UIEnableSharedImageCacheForGpu/Enabled/*UMA-NonUniformity-Trial-1-Percent/group_01/UMA-Pseudo-Metrics-Effect-Injection-25-Percent/BigEffect_01/UkmSamplingRate/Sampled_NoSeed_Other/UnifiedAutoplay/Enabled/UnimportantFramePolicy/UnimportantFrame_LowerPriority/UnlockDatabaseOnClose/Enabled/UnoDesktopBookmarksAndReadingList/Enabled/UnoDesktopHistorySyncPillExperiment/EnabledWithBrowseAcrossDevices/*UseAdHocSigningForWebAppShims/Enabled/*UseBoringSSLForRandBytes/Enabled/*UseSCContentSharingPicker/Enabled/UseSmartRefForGPUFenceHandle/Enabled/UseSnappyForParkableStrings/Enabled/UserBypassUI/Enabled/UserEducationExperienceVersion2Point5/Enabled/V8CodeFlushing/Time180Sparkplug/V8DiscardMemoryPoolBeforeMemoryPressureGcs/Enabled/V8EfficiencyModeTiering/Delay10k/V8ExternalMemoryAccountedInGlobalLimit/Enabled/V8GCSpeedUsesCounters/Enabled/V8IncrementalMarkingStartUserVisible/Enabled/V8IntelJCCErratumMitigation/EnabledNoSlowHistogram/V8ManagedZoneMemory/Enabled/V8SideStepTransitions/Enabled/V8SingleThreadedGCInBackgroundVariants/Enabled/V8SlowHistograms/Control/V8WasmDeoptCallIndirectInlining/DeoptAndInlining/VSyncAlignedPresent/Enabled_for_all_frames/VSyncDecoding/Enabled/VerifyDidCommitParams/Enabled/VisibilityAwareResourceScheduler/Enabled_20230807/VisitedURLRankingService/VisitedURLRankingService/WaitForLateScrollEvents/DeadlineRatio0_5/WebAudioBypassOutputBuffering/Enabled/WebContentsDiscard/Enabled/*WebGPU/Enabled/WebRTC-Aec3BufferingMaxAllowedExcessRenderBlocksOverride/1/WebRTC-Aec3DelayEstimateSmoothingDelayFoundOverride/0.1/WebRTC-Aec3TransparentModeHmm/Enabled/WebRTC-Audio-GainController2/Enabled,switch_to_agc2:true,target_range_min_dbfs:-50,target_range_max_dbfs:-30,max_gain_db:50,initial_gain_db:15,max_gain_change_db_per_second:6,headroom_db:5,enable_clipping_predictor:true,disallow_transient_suppressor_usage:true,_20230614/WebRTC-Audio-NetEqDecisionLogicConfig/enable_stable_playout_delay:true,reinit_after_expands:1000/WebRTC-Audio-OpusAvoidNoisePumpingDuringDtx/Enabled/WebRTC-BurstyPacer/burst:20ms,_V1/WebRTC-Bwe-LossBasedBweV2/Enabled:true,InstantUpperBoundBwBalance:100kbps,InherentLossUpperBoundBwBalance:100kbps,LossThresholdOfHighBandwidthPreference:0.2,UseByteLossRate:true,ObservationWindowSize:15,BwRampupUpperBoundFactor:1.5,BwRampupUpperBoundInHoldFactor:1.2,BwRampupUpperBoundHoldThreshold:1.3,BoundBestCandidate:true,UseInStartPhase:true,LowerBoundByAckedRateFactor:1.0,HoldDurationFactor:2.0,PaddingDuration:2000ms,PaceAtLossBasedEstimate:true/WebRTC-Bwe-ProbingConfiguration/network_state_probe_duration:200ms,network_state_interval:10s,min_probe_delta:20ms,est_lower_than_network_ratio:0.7,skip_if_est_larger_than_fraction_of_max:0.9,alr_scale:1.5,step_size:1.5/WebRTC-Bwe-ReceiverLimitCapsOnly/Enabled/WebRTC-Bwe-RobustThroughputEstimatorSettings/enabled:true,_V1/WebRTC-ForceDtls13/Enabled,_20250207/WebRTC-IPv6NetworkResolutionFixes/Enabled,ResolveStunHostnameForFamily:true,PreferGlobalIPv6Address:true,DiversifyIpv6Interfaces:true,_20220929/WebRTC-JitterEstimatorConfig/num_stddev_delay_clamp:5,num_stddev_delay_outlier:2,num_stddev_size_outlier:2,estimate_noise_when_congested:false,_20230118_BETA/WebRTC-SendBufferSizeBytes/262144,_V1/WebRTC-SendPacketsOnWorkerThread/Enabled,_20221205/WebRTC-SlackedTaskQueuePacedSender/Enabled,max_queue_time:75ms,_V4/WebRTC-UseAbsCapTimeForG2gMetric/Enabled_20250408/WebRTC-VP8ConferenceTemporalLayers/2_V1/WebRTC-Video-AV1EvenPayloadSizes/Enabled/WebRTC-Video-EnableRetransmitAllLayers/Enabled,_20230607_BETA/WebRTC-Video-H26xPacketBuffer/Enabled/WebRTC-Video-ReceiveAndSendH265/Enabled_SendReceive/WebRTC-Vp9ExternalRefCtrl/Enabled/WebRTC-ZeroPlayoutDelay/min_pacing:0ms,max_decode_queue_size:8,/WebRtcAudioSinkUseTimestampAligner/Enabled/WebRtcEncodedTransformDirectCallback/Enabled/WebUIInProcessResourceLoading/Enabled_NoScriptStreaming/WhatsNewHats/Enabled_en_20241016/WhatsNewSparkEdition/Enabled_benefits/ZPSPrefetchDebouncingDesktop/Enabled_300ms_FromLastRun/ZeroCopyTabCaptureStudyMac/Enabled_20220901/ZeroScrollMetricsUpdate/Enabled"}