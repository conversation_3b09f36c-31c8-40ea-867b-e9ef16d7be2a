// For adBlocker
import chalk from "chalk";
import puppeteer from "puppeteer";
import getBalance from "./actions/getBalance.js";
import doLogin from "./actions/doLogin.js";
import getCaptchaImage from "./actions/getCaptchaImage.js";
import zlib from 'zlib'

export default async (request, reply) => {
  try {
    const { username, password, number } = request.body;
    console.log(chalk.green("IP:", chalk.red(request.ip)));
    console.log(chalk.green("Username:", chalk.yellow(username)));

    let browser;

    try {
      browser = await puppeteer.launch({
        headless: false,
        defaultViewport: null,
        args: [`--window-size=1920,1000`, `--no-sandbox`],
        protocolTimeout: 60000,
        userDataDir: `./users/${username}`,
      });
    } catch (e) {
      return reply
        .code(400)
        .send({ success: false, message: "Cannot launch browser instance!" });
    }

    // Close all opened pages
    const pages = await browser.pages();
    for (const page of pages) {
      await page.close();
    }

    const page = await browser.newPage();
    let client = await page.createCDPSession();

    await client.send("Fetch.enable", {
      patterns: [
        { resourceType: "Document" },
        {
          urlPattern: "*",
          // resourceType: "Document",
          resourceType: "XHR",
          // requestStage: "Request",
          requestStage: "Response",
        },
      ],
    });

    await client.on("Fetch.requestPaused", async (event) => {
      let { requestId } = event;

      if (
        [
          "https://online.mbbank.com.vn/api/retail-web-internetbankingms/getCaptchaImage",
          "https://online.mbbank.com.vn/api/retail_web/internetbanking/v2.0/doLogin",
          "https://online.mbbank.com.vn/api/retail-web-accountms/getBalance",
        ].includes(event.request.url)
      ) {
        let responseData = null;

        try {
          responseData = await client.send("Fetch.getResponseBody", {
            requestId,
          });
        } catch (e) {}

        if (!responseData || !responseData.body) {
          await browser.close();
          return reply
            .code(400)
            .send({ success: false, message: "No body data found!" });
        }

        const buff = Buffer.from(
          responseData.body,
          responseData.base64Encoded ? "base64" : "utf8",
        );


        
        console.log(event.request.url, responseData, buff.toString("utf-8"))



        try {
          const body = JSON.parse(buff.toString("utf-8"));

          console.log(body)

          if (
            event.request.url ===
            "https://online.mbbank.com.vn/api/retail-web-internetbankingms/getCaptchaImage"
          ) {
            getCaptchaImage(
              request,
              reply,
              browser,
              page,
              event,
              body,
              username,
              password,
            );
          }

          if (
            event.request.url ===
            "https://online.mbbank.com.vn/api/retail_web/internetbanking/v2.0/doLogin"
          ) {
            doLogin(request, reply, browser, page, event, body, number);
          }

          if (
            event.request.url ===
            "https://online.mbbank.com.vn/api/retail-web-accountms/getBalance"
          ) {
            getBalance(
              request,
              reply,
              browser,
              page,
              event,
              body,
              username,
              number,
            );
          }
        } catch (e) {
          console.log(
            chalk.red("Error getCaptchaImage doLogin getBalance: ", e.message),
          );
          await browser.close();
          return reply
            .code(400)
            .send({ success: false, message: "No body data found!" });
        }
      }

      try {
        await client.send("Fetch.continueRequest", { requestId });
      } catch (e) {
        console.log(chalk.red("Error: ", e.message));
      }
    });

    try {
      page
        .goto("https://online.mbbank.com.vn/pl/login", {
          timeout: 30000,
        })
        .then(async (response) => {
          if (!response.ok()) {
            await browser.close();
            return reply.code(400).send({
              success: false,
              message: "Cannot open bank app, response not ok",
            });
          }
        })
        .catch(async (e) => {
          await browser.close();
          return reply.code(400).send({
            success: false,
            message: `Cannot open bank app, reason: ${e.message}`,
          });
        });
    } catch (e) {
      await browser.close();
      return reply.code(400).send({
        success: false,
        message: `Cannot open bank app, unexpected error: ${e.message}`,
      });
    }
  } catch (e) {
    return reply.code(400).send({
      success: false,
      message: e.message ?? "Cannot launch browser instance!",
    });
  }
};
